---
// Payment status page - handles redirects from PhonePe payment gateway
export const prerender = false;

// Get transaction ID from URL parameters
const url = new URL(Astro.request.url);
const transactionId = url.searchParams.get('transactionId');

// If no transaction ID, redirect to home
if (!transactionId) {
  return Astro.redirect('/');
}
---

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Payment Status - Srikar Publications</title>
  <link rel="stylesheet" href="/styles/global.css">
  <link rel="stylesheet" href="/styles/checkout.css">
  <style>
    .status-container {
      max-width: 600px;
      margin: 2rem auto;
      padding: 2rem;
      text-align: center;
    }
    
    .loading-spinner {
      border: 4px solid #f3f3f3;
      border-top: 4px solid #5f259f;
      border-radius: 50%;
      width: 50px;
      height: 50px;
      animation: spin 1s linear infinite;
      margin: 2rem auto;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    .status-success {
      color: #28a745;
      background: #d4edda;
      border: 1px solid #c3e6cb;
      border-radius: 8px;
      padding: 2rem;
      margin: 1rem 0;
    }
    
    .status-failed {
      color: #dc3545;
      background: #f8d7da;
      border: 1px solid #f5c6cb;
      border-radius: 8px;
      padding: 2rem;
      margin: 1rem 0;
    }
    
    .status-pending {
      color: #856404;
      background: #fff3cd;
      border: 1px solid #ffeaa7;
      border-radius: 8px;
      padding: 2rem;
      margin: 1rem 0;
    }
    
    .order-details {
      background: #f8f9fa;
      border-radius: 8px;
      padding: 1.5rem;
      margin: 1rem 0;
      text-align: left;
    }
    
    .action-buttons {
      margin: 2rem 0;
    }
    
    .btn {
      display: inline-block;
      padding: 12px 24px;
      margin: 0 8px;
      border: none;
      border-radius: 6px;
      text-decoration: none;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
    }
    
    .btn-primary {
      background: #5f259f;
      color: white;
    }
    
    .btn-primary:hover {
      background: #4a1d7a;
    }
    
    .btn-secondary {
      background: #6c757d;
      color: white;
    }
    
    .btn-secondary:hover {
      background: #545b62;
    }
    
    .btn-success {
      background: #28a745;
      color: white;
    }
    
    .btn-success:hover {
      background: #218838;
    }
    
    .error-message {
      color: #dc3545;
      background: #f8d7da;
      border: 1px solid #f5c6cb;
      border-radius: 4px;
      padding: 1rem;
      margin: 1rem 0;
    }
    
    .hidden {
      display: none;
    }
  </style>
</head>
<body>
  <div class="status-container">
    <!-- Loading State -->
    <div id="loading-state">
      <div class="loading-spinner"></div>
      <h2>Verifying Payment Status...</h2>
      <p>Please wait while we confirm your payment with the bank.</p>
    </div>

    <!-- Success State -->
    <div id="success-state" class="hidden">
      <div class="status-success">
        <h1>✅ Payment Successful!</h1>
        <p>Your payment has been processed successfully.</p>
      </div>
      
      <div class="order-details" id="success-details">
        <!-- Order details will be populated here -->
      </div>
      
      <div class="action-buttons">
        <a href="/orders" class="btn btn-success">View My Orders</a>
        <a href="/" class="btn btn-secondary">Continue Shopping</a>
      </div>
    </div>

    <!-- Failed State -->
    <div id="failed-state" class="hidden">
      <div class="status-failed">
        <h1>❌ Payment Failed</h1>
        <p>Unfortunately, your payment could not be processed.</p>
      </div>
      
      <div class="order-details" id="failed-details">
        <!-- Error details will be populated here -->
      </div>
      
      <div class="action-buttons">
        <button id="retry-payment" class="btn btn-primary">Retry Payment</button>
        <a href="/" class="btn btn-secondary">Back to Home</a>
      </div>
    </div>

    <!-- Pending State -->
    <div id="pending-state" class="hidden">
      <div class="status-pending">
        <h1>⏳ Payment Pending</h1>
        <p>Your payment is being processed. This may take a few minutes.</p>
      </div>
      
      <div class="order-details" id="pending-details">
        <!-- Pending details will be populated here -->
      </div>
      
      <div class="action-buttons">
        <button id="refresh-status" class="btn btn-primary">Refresh Status</button>
        <a href="/orders" class="btn btn-secondary">View Orders</a>
      </div>
    </div>

    <!-- Error State -->
    <div id="error-state" class="hidden">
      <div class="error-message">
        <h2>Error</h2>
        <p id="error-message">Something went wrong while checking your payment status.</p>
      </div>
      
      <div class="action-buttons">
        <button id="retry-check" class="btn btn-primary">Try Again</button>
        <a href="/" class="btn btn-secondary">Go Home</a>
      </div>
    </div>
  </div>

  <script>
    // Get transaction ID from URL
    const urlParams = new URLSearchParams(window.location.search);
    const transactionId = urlParams.get('transactionId');
    
    if (!transactionId) {
      window.location.href = '/';
    }

    // State management
    let currentOrder = null;
    let currentTransaction = null;

    // Show specific state
    function showState(stateName) {
      const states = ['loading', 'success', 'failed', 'pending', 'error'];
      states.forEach(state => {
        const element = document.getElementById(`${state}-state`);
        if (element) {
          element.classList.toggle('hidden', state !== stateName);
        }
      });
    }

    // Format currency
    function formatCurrency(amount) {
      return new Intl.NumberFormat('en-IN', {
        style: 'currency',
        currency: 'INR'
      }).format(amount);
    }

    // Format date
    function formatDate(dateString) {
      return new Date(dateString).toLocaleString('en-IN');
    }

    // Check payment status
    async function checkPaymentStatus(verify = false) {
      try {
        showState('loading');
        
        const url = `/api/payments/status?transactionId=${transactionId}${verify ? '&verify=true' : ''}`;
        const response = await fetch(url);
        const data = await response.json();

        if (!data.success) {
          throw new Error(data.message || 'Failed to check payment status');
        }

        currentTransaction = data.transaction;
        currentOrder = data.order;

        // Handle different payment statuses
        switch (data.transaction.status) {
          case 'completed':
            showSuccessState(data);
            break;
          case 'failed':
            showFailedState(data);
            break;
          case 'pending':
          case 'initiated':
            showPendingState(data);
            break;
          default:
            showPendingState(data);
        }

      } catch (error) {
        console.error('Error checking payment status:', error);
        showErrorState(error.message);
      }
    }

    // Show success state
    function showSuccessState(data) {
      showState('success');
      
      const detailsHtml = `
        <h3>Order Details</h3>
        <p><strong>Order ID:</strong> #${data.order?.id || 'N/A'}</p>
        <p><strong>Transaction ID:</strong> ${data.transaction.payment_id}</p>
        <p><strong>Amount:</strong> ${formatCurrency(data.transaction.amount)}</p>
        <p><strong>Payment Date:</strong> ${formatDate(data.transaction.updated_at)}</p>
        ${data.phonepe ? `<p><strong>PhonePe Order ID:</strong> ${data.phonepe.orderId}</p>` : ''}
      `;
      
      document.getElementById('success-details').innerHTML = detailsHtml;
    }

    // Show failed state
    function showFailedState(data) {
      showState('failed');
      
      const detailsHtml = `
        <h3>Payment Details</h3>
        <p><strong>Transaction ID:</strong> ${data.transaction.payment_id}</p>
        <p><strong>Amount:</strong> ${formatCurrency(data.transaction.amount)}</p>
        <p><strong>Status:</strong> Failed</p>
        ${data.phonepe?.errorCode ? `<p><strong>Error:</strong> ${data.phonepe.errorCode}</p>` : ''}
        <p><em>Don't worry, no money has been deducted from your account.</em></p>
      `;
      
      document.getElementById('failed-details').innerHTML = detailsHtml;
    }

    // Show pending state
    function showPendingState(data) {
      showState('pending');
      
      const detailsHtml = `
        <h3>Payment Details</h3>
        <p><strong>Transaction ID:</strong> ${data.transaction.payment_id}</p>
        <p><strong>Amount:</strong> ${formatCurrency(data.transaction.amount)}</p>
        <p><strong>Status:</strong> Processing</p>
        <p><strong>Initiated:</strong> ${formatDate(data.transaction.created_at)}</p>
      `;
      
      document.getElementById('pending-details').innerHTML = detailsHtml;
    }

    // Show error state
    function showErrorState(message) {
      showState('error');
      document.getElementById('error-message').textContent = message;
    }

    // Retry payment
    async function retryPayment() {
      if (!currentOrder) {
        alert('Order information not available');
        return;
      }
      
      try {
        const response = await fetch('/api/payments/initiate', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            order_id: currentOrder.id
          })
        });
        
        const data = await response.json();
        
        if (data.success && data.redirectUrl) {
          window.location.href = data.redirectUrl;
        } else {
          alert(data.message || 'Failed to initiate payment');
        }
      } catch (error) {
        console.error('Error retrying payment:', error);
        alert('Failed to retry payment. Please try again.');
      }
    }

    // Event listeners
    document.getElementById('retry-payment')?.addEventListener('click', retryPayment);
    document.getElementById('refresh-status')?.addEventListener('click', () => checkPaymentStatus(true));
    document.getElementById('retry-check')?.addEventListener('click', () => checkPaymentStatus(true));

    // Initial status check
    checkPaymentStatus(true);

    // Auto-refresh for pending payments
    let refreshInterval;
    function startAutoRefresh() {
      refreshInterval = setInterval(() => {
        if (currentTransaction?.status === 'pending' || currentTransaction?.status === 'initiated') {
          checkPaymentStatus(true);
        } else {
          clearInterval(refreshInterval);
        }
      }, 10000); // Check every 10 seconds
    }

    // Start auto-refresh after initial check
    setTimeout(startAutoRefresh, 5000);

    // Clear interval when page is unloaded
    window.addEventListener('beforeunload', () => {
      if (refreshInterval) {
        clearInterval(refreshInterval);
      }
    });
  </script>
</body>
</html>
