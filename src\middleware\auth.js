import { verifyJWT } from "../utils/auth";

/**
 * Authentication middleware for Astro API routes
 * @returns {Response|undefined} Returns a 401 Response if unauthorized, undefined to continue
 */
export async function authMiddleware({ request }) {
  try {
    // Get the authorization header
    const authHeader = request.headers.get("Authorization");

    // Check if the authorization header exists and starts with 'Bearer '
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      // Also check for auth cookie as fallback
      const cookies = request.headers.get("Cookie") || "";
      const sessionMatch = cookies.match(/session=([^;]+)/);

      if (!sessionMatch) {
        return new Response(JSON.stringify({ error: "Unauthorized" }), {
          status: 401,
          headers: { "Content-Type": "application/json" },
        });
      }

      // Verify the session cookie (token)
      const sessionToken = sessionMatch[1];
      const payload = await verifyJWT(sessionToken);

      if (!payload) {
        return new Response(JSON.stringify({ error: "Invalid session" }), {
          status: 401,
          headers: { "Content-Type": "application/json" },
        });
      }

      // Add the user to the request context
      return { user: payload };
    }

    // Extract the token
    const token = authHeader.split(" ")[1];

    // Verify the token
    const payload = await verifyJWT(token);

    if (!payload) {
      return new Response(JSON.stringify({ error: "Invalid token" }), {
        status: 401,
        headers: { "Content-Type": "application/json" },
      });
    }

    // Add the user to the request context
    return { user: payload };
  } catch (error) {
    console.error("Auth middleware error:", error);
    return new Response(JSON.stringify({ error: "Authentication error" }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
}

/**
 * Admin authentication middleware for Astro API routes
 * Checks for admin session cookie and validates admin access
 * @returns {Response|Object} Returns a 401/403 Response if unauthorized or not admin, or user object if admin
 */
export async function adminAuthMiddleware({ request, locals }) {
  try {
    // Check for admin session cookie
    const cookies = request.headers.get("Cookie") || "";
    const adminSessionMatch = cookies.match(/admin_session=([^;]+)/);

    if (!adminSessionMatch) {
      return new Response(JSON.stringify({ error: "Admin authentication required" }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Verify the admin session token
    const adminToken = adminSessionMatch[1];
    const payload = await verifyJWT(adminToken);

    if (!payload) {
      return new Response(JSON.stringify({ error: "Invalid admin session" }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Check if the token is for admin user
    if (!payload.isAdmin || payload.role !== 'admin') {
      return new Response(JSON.stringify({ error: "Not authorized as admin" }), {
        status: 403,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Admin is authenticated, return admin user data
    return {
      user: {
        id: payload.id,
        email: payload.email,
        role: payload.role,
        isAdmin: payload.isAdmin
      },
    };
  } catch (error) {
    console.error("Admin auth middleware error:", error);
    return new Response(
      JSON.stringify({ error: "Admin authentication error" }),
      {
        status: 500,
        headers: { "Content-Type": "application/json" },
      }
    );
  }
}
