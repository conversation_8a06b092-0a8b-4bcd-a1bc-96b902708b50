import type { APIRoute } from 'astro';

export const prerender = false;

export const POST: APIRoute = async ({ request }) => {
  try {
    // Clear the admin session cookie
    const response = new Response(JSON.stringify({
      success: true,
      message: 'Admin logout successful'
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

    // Clear admin session cookie by setting it to expire immediately
    response.headers.append('Set-Cookie', 
      `admin_session=; HttpOnly; Secure; SameSite=Strict; Path=/; Max-Age=0`
    );

    return response;

  } catch (error) {
    console.error('Admin logout error:', error);
    return new Response(JSON.stringify({ 
      error: 'Internal server error' 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
