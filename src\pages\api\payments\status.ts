import type { APIRoute } from 'astro';
import {
  getPaymentTransactionByPaymentId,
  updatePaymentTransaction,
  updateOrderPaymentStatus,
  updateOrderStatus,
  getAdminOrderById
} from '../../../db/database';
import { authMiddleware } from '../../../middleware/auth';
import axios from 'axios';

export const prerender = false;

// PhonePe v2 API configuration
const getPhonePeConfig = (env: any) => ({
  PHONEPE_API_URL:  "https://api.phonepe.com/apis/pg",
  PHONEPE_AUTH_URL:   "https://api.phonepe.com/apis/identity-manager/v1/oauth",
  MERCHANT_ID:   "M23LZOY7DEJCI", // Your production merchant ID
  // v2 API uses client credentials for OAuth
  CLIENT_ID:   "SU2507051851592568452462",
  CLIENT_SECRET:   "d1c16b8b-8638-45ff-aa09-f93d7f693720",
  // Keep v1 config for backward compatibility if needed
  SALT_KEY:   "d1c16b8b-8638-45ff-aa09-f93d7f693720",
  SALT_INDEX:  "1"
});

/**
 * Get access token for PhonePe v2 API
 */
async function getPhonePeAccessToken(config: any): Promise<string> {
  try {
    const authPayload = {
      grant_type: "client_credentials",
      client_id: config.CLIENT_ID,
      client_secret: config.CLIENT_SECRET
    };

    const response = await axios.post(`${config.PHONEPE_AUTH_URL}/token`, authPayload, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json'
      }
    });

    if (response.data && response.data.access_token) {
      return response.data.access_token;
    } else {
      throw new Error('No access token received from PhonePe auth API');
    }
  } catch (error: any) {
    console.error('PhonePe authorization failed:', error.response?.data || error.message);
    throw new Error(`Failed to get PhonePe access token: ${error.response?.data?.message || error.message}`);
  }
}

/**
 * Get status of a payment transaction with PhonePe v2 verification
 * This endpoint is used to check payment status after user returns from payment gateway
 */
export const GET: APIRoute = async ({ request, locals }) => {
  try {
    // Get PhonePe configuration
    const config = getPhonePeConfig(locals.runtime.env);

    // Authenticate user
    const authResult = await authMiddleware({ request });
    if (authResult instanceof Response) {
      return authResult; // Return auth error response
    }

    // Get transaction ID from query parameters
    const url = new URL(request.url);
    const transactionId = url.searchParams.get('transactionId');
    const verify = url.searchParams.get('verify') === 'true'; // Optional parameter to force verification

    if (!transactionId) {
      return new Response(JSON.stringify({
        success: false,
        message: 'Transaction ID is required'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get transaction from database
    const transaction = await getPaymentTransactionByPaymentId(
      locals.runtime.env,
      transactionId
    );

    if (!transaction) {
      return new Response(JSON.stringify({
        success: false,
        message: 'Transaction not found'
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // If transaction is still pending or if verification is requested, verify with PhonePe
    if (transaction.status === 'initiated' || transaction.status === 'pending' || verify) {
      try {
        // Get access token for PhonePe v2 API
        const accessToken = await getPhonePeAccessToken(config);

        // Verify payment status with PhonePe v2 API
        const response = await axios.get(
          `${config.PHONEPE_API_URL}/checkout/v2/order/${transactionId}/status?details=true&errorContext=true`,
          {
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `O-Bearer ${accessToken}`
            }
          }
        );

        const phonepeResponse = response.data;
        console.log('PhonePe v2 Status Response:', JSON.stringify(phonepeResponse, null, 2));

        // Process the PhonePe response and update database
        const phonepeState = phonepeResponse.state; // PENDING, COMPLETED, FAILED
        let newTransactionStatus: string;
        let orderPaymentStatus: string;
        let orderStatus: string;

        switch (phonepeState) {
          case 'COMPLETED':
            newTransactionStatus = 'completed';
            orderPaymentStatus = 'paid';
            orderStatus = 'confirmed';
            break;
          case 'FAILED':
            newTransactionStatus = 'failed';
            orderPaymentStatus = 'failed';
            orderStatus = 'pending'; // Keep order as pending so user can retry
            break;
          case 'PENDING':
          default:
            newTransactionStatus = 'pending';
            orderPaymentStatus = 'pending';
            orderStatus = 'pending';
            break;
        }

        // Update transaction status in our database
        await updatePaymentTransaction(locals.runtime.env, transactionId, {
          status: newTransactionStatus,
          gateway_response: JSON.stringify(phonepeResponse)
        });

        // Update order payment status
        await updateOrderPaymentStatus(
          locals.runtime.env,
          transaction.order_id,
          orderPaymentStatus
        );

        // Update order status if payment was successful
        if (phonepeState === 'COMPLETED') {
          await updateOrderStatus(
            locals.runtime.env,
            transaction.order_id,
            orderStatus
          );
        }

        // Get updated order details
        const order = await getAdminOrderById(locals.runtime.env, transaction.order_id);

        return new Response(JSON.stringify({
          success: true,
          transaction: {
            id: transaction.id,
            order_id: transaction.order_id,
            payment_id: transaction.payment_id,
            amount: transaction.amount,
            status: newTransactionStatus,
            created_at: transaction.created_at,
            updated_at: new Date().toISOString()
          },
          order: order ? {
            id: order.id,
            order_status: order.order_status,
            payment_status: order.payment_status,
            total_amount: order.total_amount
          } : null,
          phonepe: {
            orderId: phonepeResponse.orderId,
            state: phonepeResponse.state,
            amount: phonepeResponse.amount,
            expireAt: phonepeResponse.expireAt,
            paymentDetails: phonepeResponse.paymentDetails || []
          },
          verified: true,
          message: `Payment ${phonepeState.toLowerCase()}`
        }), {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache, no-store, must-revalidate'
          }
        });

      } catch (error: any) {
        console.error('PhonePe verification failed:', error.response?.data || error.message);

        // If verification fails, return the current database status
        // This handles cases where PhonePe API is down or order has expired
        return new Response(JSON.stringify({
          success: true,
          transaction: {
            id: transaction.id,
            order_id: transaction.order_id,
            payment_id: transaction.payment_id,
            amount: transaction.amount,
            status: transaction.status,
            created_at: transaction.created_at,
            updated_at: transaction.updated_at
          },
          verified: false,
          verificationError: error.response?.data || error.message,
          message: 'Returned cached status due to verification failure'
        }), {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache, no-store, must-revalidate'
          }
        });
      }
    }

    // Return current transaction status from database
    return new Response(JSON.stringify({
      success: true,
      transaction: {
        id: transaction.id,
        order_id: transaction.order_id,
        payment_id: transaction.payment_id,
        amount: transaction.amount,
        status: transaction.status,
        created_at: transaction.created_at,
        updated_at: transaction.updated_at
      },
      verified: false,
      message: 'Returned cached status'
    }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      }
    });

  } catch (error) {
    console.error('Error checking payment status:', error);
    return new Response(JSON.stringify({
      success: false,
      message: 'Failed to check payment status'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};