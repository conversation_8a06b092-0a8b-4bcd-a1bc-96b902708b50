---
interface Props {
  title?: string;
  description?: string;
}

const {
  title = "Admin Dashboard - Sreekar Publishers",
  description = "Sreekar Publishers Admin Dashboard",
} = Astro.props;

import "../styles/global.css";
---

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" type="image/jpeg" href="/images/sreekarpublishers-logo.jpeg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content={description} />
    <title>{title}</title>
    <link
      href="https://fonts.googleapis.com/icon?family=Material+Icons+Round"
      rel="stylesheet"
    />
  </head>
  <body class="bg-gray-50">
    <div class="flex h-screen overflow-hidden">
      <!-- Sidebar -->
      <aside
        id="sidebar"
        class="bg-white w-64 shadow-sm border-r border-gray-200 fixed inset-y-0 left-0 z-20 transition-transform duration-300 transform lg:translate-x-0"
      >
        <div class="flex items-center justify-between h-16 px-4 border-b">
          <div class="flex items-center">
            <div class="w-8 h-8 rounded-md overflow-hidden mr-2">
              <img
                src="/images/sreekarpublishers-logo.jpeg"
                alt="Sreekar Publishers Logo"
                width="32"
                height="32"
                class="w-full h-full object-cover"
              />
            </div>
            <h1 class="font-bold text-xl text-gray-800">Sreekar Publishers</h1>
          </div>
          <button
            id="close-sidebar"
            class="lg:hidden text-gray-500 focus:outline-none"
          >
            <span class="material-icons-round">close</span>
          </button>
        </div>
        <nav class="py-4">
          <div class="px-4 mb-2">
            <p
              class="text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Main
            </p>
          </div>
          <a
            href="/admin"
            class="flex items-center px-4 py-2.5 text-gray-700 hover:bg-orange-50 hover:text-orange-600 sidebar-link"
            data-link="dashboard"
          >
            <span class="material-icons-round mr-3 text-gray-500"
              >dashboard</span
            >
            Dashboard
          </a>
          <a
            href="/admin/orders"
            class="flex items-center px-4 py-2.5 text-gray-700 hover:bg-orange-50 hover:text-orange-600 sidebar-link"
            data-link="orders"
          >
            <span class="material-icons-round mr-3 text-gray-500"
              >receipt_long</span
            >
            Orders
          </a>
          <a
            href="/admin/products"
            class="flex items-center px-4 py-2.5 text-gray-700 hover:bg-orange-50 hover:text-orange-600 sidebar-link"
            data-link="products"
          >
            <span class="material-icons-round mr-3 text-gray-500"
              >inventory_2</span
            >
            Products
          </a>
          <a
            href="/admin/categories"
            class="flex items-center px-4 py-2.5 text-gray-700 hover:bg-orange-50 hover:text-orange-600 sidebar-link"
            data-link="categories"
          >
            <span class="material-icons-round mr-3 text-gray-500">category</span
            >
            Categories
          </a>
          <a
            href="/admin/customers"
            class="flex items-center px-4 py-2.5 text-gray-700 hover:bg-orange-50 hover:text-orange-600 sidebar-link"
            data-link="customers"
          >
            <span class="material-icons-round mr-3 text-gray-500">people</span>
            Customers
          </a>
          <a
            href="/admin/coupons"
            class="flex items-center px-4 py-2.5 text-gray-700 hover:bg-orange-50 hover:text-orange-600 sidebar-link"
            data-link="coupons"
          >
            <span class="material-icons-round mr-3 text-gray-500"
              >local_offer</span
            >
            Discount Coupons
          </a>
          <a
            href="/admin/payment-methods"
            class="flex items-center px-4 py-2.5 text-gray-700 hover:bg-orange-50 hover:text-orange-600 sidebar-link"
            data-link="payment-methods"
          >
            <span class="material-icons-round mr-3 text-gray-500">credit_card</span
            >
            Payment Methods
          </a>
          <a
            href="/admin/receipts"
            class="flex items-center px-4 py-2.5 text-gray-700 hover:bg-orange-50 hover:text-orange-600 sidebar-link"
            data-link="receipts"
          >
            <span class="material-icons-round mr-3 text-gray-500">picture_as_pdf</span>
            Receipts
          </a>
        </nav>
      </aside>

      <!-- Main Content -->
      <div class="flex-1 lg:ml-64 flex flex-col">
        <!-- Top Header -->
        <header
          id="header"
          class="bg-white shadow-sm h-16 flex items-center justify-between px-4 lg:px-6"
        >
          <div class="flex items-center">
            <button
              id="toggle-sidebar"
              class="text-gray-500 focus:outline-none lg:hidden"
            >
              <span class="material-icons-round">menu</span>
            </button>
            <h2
              id="page-title"
              class="ml-2 lg:ml-0 font-semibold text-xl text-gray-800"
            >
              Dashboard
            </h2>
          </div>
          <div class="flex items-center">
            <div class="relative ml-3">
              <div>
                <button
                  id="user-menu-button"
                  class="flex text-sm rounded-full focus:outline-none focus:ring-1 focus:ring-orange-500"
                >
                  <div
                    class="w-8 h-8 rounded-full bg-orange-100 flex items-center justify-center text-orange-600"
                  >
                    <span class="material-icons-round text-sm">person</span>
                  </div>
                </button>
              </div>
              <div
                id="user-dropdown"
                class="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none hidden"
              >
                <div class="py-1">
                  <div class="block px-4 py-2 text-sm text-gray-700">
                    <p class="font-semibold">Admin User</p>
                    <!-- <p class="text-gray-500"><EMAIL></p> -->
                  </div>
                  <hr class="my-1" />
                  <!-- <a
                    href="/profile"
                    class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >Your Profile</a
                  > -->
                  <button
                    id="admin-logout-btn"
                    class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-red-600"
                    >Sign out</button
                  >
                </div>
              </div>
            </div>
          </div>
        </header>

        <!-- Main Content Area -->
        <main class="flex-1 overflow-y-auto p-4 lg:p-6">
          <slot />
        </main>
      </div>
    </div>

    <script>
      // Set active page based on URL
      document.addEventListener("DOMContentLoaded", () => {
        // Check if user is admin before allowing access
        // checkAdmin();

        const path = window.location.pathname;

        // Set page title
        const pageTitleElement = document.getElementById("page-title");

        // Set active sidebar link
        const sidebarLinks = document.querySelectorAll(".sidebar-link");        sidebarLinks.forEach((link) => {
          const href = link.getAttribute("href");
          if (path === href) {
            link.classList.add("bg-orange-50", "text-orange-600");
            const linkElement = link as HTMLElement;
            if (linkElement.dataset.link && pageTitleElement) {
              // Set page title based on active link
              switch (linkElement.dataset.link) {
                case "dashboard":
                  pageTitleElement.textContent = "Dashboard";
                  break;
                case "orders":
                  pageTitleElement.textContent = "Orders Management";
                  break;
                case "products":
                  pageTitleElement.textContent = "Products Management";
                  break;
                case "categories":
                  pageTitleElement.textContent = "Categories Management";
                  break;
                case "customers":
                  pageTitleElement.textContent = "Customers";
                  break;                case "promotions":
                  pageTitleElement.textContent = "Promotions";
                  break;
                case "coupons":
                  pageTitleElement.textContent = "Discount Coupons";
                  break;
                case "payment-methods":
                  pageTitleElement.textContent = "Payment Methods";
                  break;
                default:
                  pageTitleElement.textContent = "Dashboard";
              }
            }
          }
        });

        // Toggle user dropdown
        const userMenuButton = document.getElementById("user-menu-button");
        const userDropdown = document.getElementById("user-dropdown");
        if (userMenuButton && userDropdown) {
          userMenuButton.addEventListener("click", () => {
            userDropdown.classList.toggle("hidden");
          });          // Close dropdown when clicking outside
          document.addEventListener("click", (event) => {
            const target = event.target as Node;
            if (
              !userMenuButton.contains(target) &&
              !userDropdown.contains(target)
            ) {
              userDropdown.classList.add("hidden");
            }
          });
        }

        // Handle admin logout
        const logoutBtn = document.getElementById("admin-logout-btn");
        if (logoutBtn) {
          logoutBtn.addEventListener("click", async () => {
            try {
              const response = await fetch('/api/admin/logout', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json'
                },
                credentials: 'include'
              });

              if (response.ok) {
                // Redirect to admin login page
                window.location.href = '/admin/login';
              } else {
                console.error('Logout failed');
                // Still redirect to login page even if logout API fails
                window.location.href = '/admin/login';
              }
            } catch (error) {
              console.error('Logout error:', error);
              // Redirect to login page even if there's an error
              window.location.href = '/admin/login';
            }
          });
        }

        // Toggle sidebar on mobile
        const toggleSidebarButton = document.getElementById("toggle-sidebar");
        const closeSidebarButton = document.getElementById("close-sidebar");
        const sidebar = document.getElementById("sidebar");

        if (toggleSidebarButton && closeSidebarButton && sidebar) {
          toggleSidebarButton.addEventListener("click", () => {
            sidebar.classList.toggle("-translate-x-full");
          });

          closeSidebarButton.addEventListener("click", () => {
            sidebar.classList.add("-translate-x-full");
          });
        }
      });

      // Check if user is admin
      async function checkAdmin() {
        try {
          const response = await fetch('/api/admin/check-auth', {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json'
            },
            credentials: 'include'
          });

          if (!response.ok) {
            // Redirect non-admin users to admin login page
            window.location.href = '/admin/login';
          }
        } catch (error) {
          console.error('Error checking admin status:', error);
          window.location.href = '/admin/login';
        }
      }

      // Check admin authentication on page load
      checkAdmin();
    </script>
    <script src="/scripts/api-client.js" is:inline></script>
  </body>
</html>
