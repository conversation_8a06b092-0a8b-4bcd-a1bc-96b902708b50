---
import MainLayout from "../../layouts/MainLayout.astro";
---

<MainLayout
  title="Admin Login - Sreekar Publishers"
  headerTitle="Admin Login"
  showHeader={false}
  showBackButton={true}
  showFooter={false}
>
  <div class="max-w-md mx-auto px-4 py-8">
    <div class="mb-6 text-center">
      <div class="w-20 h-20 bg-orange-100 rounded-full mx-auto flex items-center justify-center mb-4">
        <span class="material-icons-round text-[#FF6B35] text-4xl">admin_panel_settings</span>
      </div>
      <h1 class="text-2xl font-bold text-gray-800 mb-2">Admin Access</h1>
      <p class="text-gray-600">Enter admin password to continue</p>
    </div>

    <!-- Admin Login Form -->
    <div class="bg-white rounded-lg shadow-sm border p-6">
      <form id="admin-login-form">
        <div class="mb-4">
          <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
            Admin Password
          </label>
          <input
            type="password"
            id="password"
            name="password"
            required
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
            placeholder="Enter admin password"
          />
        </div>

        <button
          type="submit"
          id="login-btn"
          class="w-full bg-[#FF6B35] text-white py-2 px-4 rounded-md hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 transition-colors"
        >
          <span id="login-text">Login</span>
          <span id="login-loading" class="hidden">
            <span class="material-icons-round animate-spin text-sm">refresh</span>
            Logging in...
          </span>
        </button>
      </form>

      <!-- Error Message -->
      <div id="error-message" class="mt-4 p-3 bg-red-50 border border-red-200 rounded-md text-red-700 text-sm hidden">
        <span class="material-icons-round text-sm mr-1">error</span>
        <span id="error-text">Invalid password. Please try again.</span>
      </div>
    </div>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const form = document.getElementById('admin-login-form');
      const passwordInput = document.getElementById('password');
      const loginBtn = document.getElementById('login-btn');
      const loginText = document.getElementById('login-text');
      const loginLoading = document.getElementById('login-loading');
      const errorMessage = document.getElementById('error-message');
      const errorText = document.getElementById('error-text');

      // Hide error message when user starts typing
      passwordInput.addEventListener('input', function() {
        errorMessage.classList.add('hidden');
      });

      form.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const password = passwordInput.value.trim();
        
        if (!password) {
          showError('Please enter the admin password');
          return;
        }

        // Show loading state
        setLoading(true);
        
        try {
          const response = await fetch('/api/admin/login', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ password })
          });

          const data = await response.json();

          if (response.ok) {
            // Success - redirect to admin dashboard
            window.location.href = '/admin';
          } else {
            // Show error
            showError(data.error || 'Invalid password. Please try again.');
          }
        } catch (error) {
          console.error('Login error:', error);
          showError('An error occurred. Please try again.');
        } finally {
          setLoading(false);
        }
      });

      function setLoading(loading) {
        if (loading) {
          loginText.classList.add('hidden');
          loginLoading.classList.remove('hidden');
          loginBtn.disabled = true;
          passwordInput.disabled = true;
        } else {
          loginText.classList.remove('hidden');
          loginLoading.classList.add('hidden');
          loginBtn.disabled = false;
          passwordInput.disabled = false;
        }
      }

      function showError(message) {
        errorText.textContent = message;
        errorMessage.classList.remove('hidden');
      }

      // Focus on password input
      passwordInput.focus();
    });
  </script>
</MainLayout>
