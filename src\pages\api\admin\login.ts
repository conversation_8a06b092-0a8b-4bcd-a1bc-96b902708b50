import type { APIRoute } from 'astro';
import { generateJWT } from '../../../utils/auth';

export const prerender = false;

const ADMIN_PASSWORD = '$reekar@123';

export const POST: APIRoute = async ({ request }) => {
  try {
    const { password } = await request.json();

    if (!password) {
      return new Response(JSON.stringify({ 
        error: 'Password is required' 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Check if password matches
    if (password !== ADMIN_PASSWORD) {
      return new Response(JSON.stringify({ 
        error: 'Unauthorized - Invalid password' 
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Generate admin session token
    const adminUser = {
      id: 'admin',
      email: '<EMAIL>',
      role: 'admin',
      isAdmin: true
    };

    const token = await generateJWT(adminUser);

    // Set admin session cookie
    const response = new Response(JSON.stringify({
      success: true,
      message: 'Admin login successful',
      user: adminUser
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

    // Set secure admin session cookie
    response.headers.append('Set-Cookie', 
      `admin_session=${token}; HttpOnly; Secure; SameSite=Strict; Path=/; Max-Age=86400`
    );

    return response;

  } catch (error) {
    console.error('Admin login error:', error);
    return new Response(JSON.stringify({ 
      error: 'Internal server error' 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
