import type { APIRoute } from 'astro';
import { adminAuthMiddleware } from '../../../middleware/auth';

export const prerender = false;

/**
 * Check if the current user has admin authentication
 */
export const GET: APIRoute = async ({ request, locals }) => {
  try {
    // Validate admin authentication
    const authResult = await adminAuthMiddleware({ request, locals });
    if (authResult instanceof Response) {
      return authResult; // Return auth error response
    }

    const { user } = authResult;

    return new Response(JSON.stringify({ 
      success: true,
      isAdmin: true,
      user: {
        id: user.id,
        email: user.email,
        role: user.role
      }
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Admin auth check error:', error);
    return new Response(JSON.stringify({ 
      error: 'Admin authentication check failed' 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
