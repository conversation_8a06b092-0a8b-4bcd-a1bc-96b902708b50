import React, { useState } from "react";

export default function AdminHeader({ toggleSidebar, title = "Dashboard" }) {
  const [isProfileDropdownOpen, setProfileDropdownOpen] = useState(false);
  const [isNotificationsOpen, setNotificationsOpen] = useState(false);

  const notifications = [
    {
      id: 1,
      title: "New order received",
      description: "Order #1234 has been placed",
      time: "5 min ago",
      read: false,
    },
    {
      id: 2,
      title: "Payment successful",
      description: "Payment for order #1233 has been confirmed",
      time: "30 min ago",
      read: false,
    },
    {
      id: 3,
      title: "Order delivered",
      description: "Order #1230 has been delivered successfully",
      time: "2 hours ago",
      read: true,
    },
  ];

  // Toggle profile dropdown
  const toggleProfileDropdown = () => {
    setProfileDropdownOpen(!isProfileDropdownOpen);
    if (isNotificationsOpen) setNotificationsOpen(false);
  };

  // Toggle notifications dropdown
  const toggleNotifications = () => {
    setNotificationsOpen(!isNotificationsOpen);
    if (isProfileDropdownOpen) setProfileDropdownOpen(false);
  };

  // Handle click outside dropdowns
  const handleClickOutside = () => {
    if (isProfileDropdownOpen) setProfileDropdownOpen(false);
    if (isNotificationsOpen) setNotificationsOpen(false);
  };

  // Handle admin logout
  const handleLogout = async () => {
    try {
      const response = await fetch('/api/admin/logout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include'
      });

      if (response.ok) {
        // Redirect to admin login page
        window.location.href = '/admin/login';
      } else {
        console.error('Logout failed');
        // Still redirect to login page even if logout API fails
        window.location.href = '/admin/login';
      }
    } catch (error) {
      console.error('Logout error:', error);
      // Redirect to login page even if there's an error
      window.location.href = '/admin/login';
    }
  };

  return (
    <header
      className="bg-white border-b border-gray-200 sticky top-0 z-30"
      id="admin-header"
    >
      <div className="px-4 sm:px-6 lg:px-8 py-4 flex items-center justify-between">
        {/* Left section with mobile menu toggle and title */}
        <div className="flex items-center">
          <button
            onClick={toggleSidebar}
            className="text-gray-500 hover:text-gray-600 lg:hidden p-2 -ml-2 rounded-md focus:outline-none focus:ring-2 focus:ring-inset focus:ring-orange-500"
            aria-label="Open sidebar"
          >
            <span className="material-icons-round">menu</span>
          </button>
          <h1 className="ml-2 lg:ml-0 text-xl font-semibold text-gray-800">
            {title}
          </h1>
        </div>

        {/* Right section with search, notifications, user profile */}
        <div className="flex items-center space-x-4">
          {/* Search Box */}
          <div className="hidden md:block relative">
            <div className="flex items-center bg-gray-50 rounded-lg px-3 py-2 focus-within:ring-2 focus-within:ring-orange-500 focus-within:bg-white">
              <span className="material-icons-round text-gray-400 text-lg">
                search
              </span>
              <input
                type="text"
                placeholder="Search..."
                className="ml-2 bg-transparent border-none focus:outline-none text-sm text-gray-700 placeholder-gray-500"
              />
            </div>
          </div>

          {/* User Profile Dropdown */}
          <div className="relative">
            <button
              onClick={toggleProfileDropdown}
              className="flex items-center space-x-2 focus:outline-none"
              aria-label="User menu"
              aria-haspopup="true"
            >
              <div className="h-9 w-9 rounded-full bg-orange-500 flex items-center justify-center text-white font-medium">
                A
              </div>
              <span className="hidden md:inline-block text-sm font-medium text-gray-700">
                Admin
              </span>
              <span className="material-icons-round text-gray-400">
                arrow_drop_down
              </span>
            </button>

            {/* Profile dropdown menu */}
            {isProfileDropdownOpen && (
              <div className="origin-top-right absolute right-0 mt-2 w-56 rounded-lg shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none">
                <div
                  className="py-1"
                  role="menu"
                  aria-orientation="vertical"
                  aria-labelledby="options-menu"
                >
                  <div className="px-4 py-3 border-b border-gray-100">
                    <p className="text-sm font-medium text-gray-900">
                      Admin User
                    </p>
                    {/* <p className="text-sm text-gray-500 truncate">
                      <EMAIL>
                    </p> */}
                  </div>

                  {/* <a
                    href="/admin/profile"
                    className="flex px-4 py-2.5 text-sm text-gray-700 hover:bg-gray-50 items-center"
                  >
                    <span className="material-icons-round mr-3 text-gray-500">
                      account_circle
                    </span>
                    Your Profile
                  </a> */}

                  <div className="border-t border-gray-100 mt-1">
                    <button
                      onClick={handleLogout}
                      className="flex w-full px-4 py-2.5 text-sm text-red-600 hover:bg-red-50 items-center text-left"
                    >
                      <span className="material-icons-round mr-3 text-red-500">
                        logout
                      </span>
                      Sign out
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Overlay that closes dropdowns when clicked outside */}
      {(isProfileDropdownOpen || isNotificationsOpen) && (
        <div
          className="fixed inset-0 z-10"
          onClick={handleClickOutside}
          aria-hidden="true"
        />
      )}
    </header>
  );
}
