import type { APIRoute } from 'astro';
import { getOrderPaymentTransactions, getOrderById } from '../../../../db/database';
import { authMiddleware } from '../../../../middleware/auth';

export const prerender = false;

/**
 * Get payment transactions for a specific order
 */
export const GET: APIRoute = async ({ request, params, locals }) => {
  try {
    // Authenticate the user
    const authResult = await authMiddleware({ request });
    if (authResult instanceof Response) {
      return authResult; // Return authentication error
    }

    const user = (authResult as any).user;
    const orderId = parseInt(params.orderId || '0');

    if (!orderId || isNaN(orderId)) {
      return new Response(JSON.stringify({
        success: false,
        message: 'Valid order ID is required'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Verify the order belongs to the user
    const order = await getOrderById(locals.runtime.env, orderId, user.id);
    if (!order) {
      return new Response(JSON.stringify({
        success: false,
        message: 'Order not found or access denied'
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get payment transactions for the order
    const transactions = await getOrderPaymentTransactions(locals.runtime.env, orderId);

    // Format transactions for frontend
    const formattedTransactions = transactions.map(transaction => ({
      id: transaction.id,
      payment_id: transaction.payment_id,
      payment_method: transaction.payment_method,
      amount: transaction.amount,
      status: transaction.status,
      created_at: transaction.created_at,
      updated_at: transaction.updated_at,
      gateway_response: transaction.gateway_response ? JSON.parse(transaction.gateway_response) : null
    }));

    return new Response(JSON.stringify({
      success: true,
      transactions: formattedTransactions
    }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('Error getting payment transactions:', error);
    return new Response(JSON.stringify({
      success: false,
      message: 'Failed to get payment transactions',
      error: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
