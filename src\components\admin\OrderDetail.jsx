import React, { useState, useEffect } from "react";
import { formatToIST } from "../../utils/dateUtils";

export default function OrderDetail({ orderId }) {
  const [order, setOrder] = useState(null);
  const [orderItems, setOrderItems] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);
  const [statusUpdateSuccess, setStatusUpdateSuccess] = useState(false);
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [isUpdatingDeliveryFee, setIsUpdatingDeliveryFee] = useState(false);
  const [newDeliveryFee, setNewDeliveryFee] = useState(0);
  const [paymentTransactions, setPaymentTransactions] = useState([]);
  const [loadingPaymentStatus, setLoadingPaymentStatus] = useState(false);
  const [retryingPayment, setRetryingPayment] = useState(false);

  // Fetch order details on component mount or when orderId changes
  useEffect(() => {
    async function fetchOrderDetails() {
      if (!orderId) return;

      try {
        setLoading(true);
        setError(null);

        const orderDetails = await window.ApiClient.getAdminOrderDetails(
          orderId
        );

        if (orderDetails.order) {
          setOrder(orderDetails.order);
          setOrderItems(orderDetails.order.items || []);
          // Fetch payment transactions for this order
          fetchPaymentTransactions(orderId);
        } else {
          throw new Error("Order not found");
        }
      } catch (err) {
        console.error("Error fetching order details:", err);
        setError(
          `Failed to load order details: ${err.message || "Unknown error"}`
        );
      } finally {
        setLoading(false);
      }
    }

    fetchOrderDetails();
  }, [orderId]);

  // Handle order status update
  const handleStatusChange = async (newStatus) => {
    try {
      setIsUpdatingStatus(true);
      setStatusUpdateSuccess(false);

      // Call API to update order status
      const result = await window.ApiClient.updateOrderStatus(
        orderId,
        newStatus
      );

      if (result.success) {
        // Update the order in state
        setOrder((prev) => ({
          ...prev,
          order_status: newStatus,
        }));
        setStatusUpdateSuccess(true);

        // Clear success message after a few seconds
        setTimeout(() => {
          setStatusUpdateSuccess(false);
        }, 3000);
      } else {
        throw new Error(result.message || "Failed to update status");
      }
    } catch (err) {
      console.error(`Error updating order status:`, err);
      setError(
        `Failed to update order status: ${err.message || "Unknown error"}`
      );

      // Clear error after a few seconds
      setTimeout(() => {
        setError(null);
      }, 4000);
    } finally {
      setIsUpdatingStatus(false);
    }
  };

  // Show notification with proper icon
  const showNotification = (message, type = "success") => {
    if (typeof window !== "undefined") {
      let toastContainer = document.querySelector(".toast-container");

      if (!toastContainer) {
        toastContainer = document.createElement("div");
        toastContainer.className =
          "toast-container fixed bottom-24 sm:bottom-20 left-0 right-0 flex flex-col items-center z-50 pointer-events-none px-4";
        document.body.appendChild(toastContainer);
      }

      const toast = document.createElement("div");
      toast.className =
        "bg-gray-800 text-white px-4 py-3 rounded-xl text-sm font-medium shadow-lg opacity-0 transition-all duration-300 transform translate-y-4 mb-2 flex items-center max-w-md w-full justify-center";

      const icon = document.createElement("span");
      icon.className = "material-icons-round mr-2 text-[16px] md:text-[18px]";

      if (type === "error") {
        icon.textContent = "error";
      } else if (type === "success") {
        icon.textContent = "check_circle";
      } else {
        icon.textContent = "info";
      }

      toast.appendChild(icon);

      const textSpan = document.createElement("span");
      textSpan.textContent = message;
      toast.appendChild(textSpan);

      toastContainer.appendChild(toast);

      setTimeout(() => {
        toast.classList.remove("opacity-0", "translate-y-4");
        toast.classList.add("opacity-95");
      }, 10);

      setTimeout(() => {
        toast.classList.add("opacity-0", "translate-y-4");
        setTimeout(() => {
          toast.remove();
        }, 300);
      }, 3000);
    }
  };

  const handlePaymentStatusUpdate = async (status) => {
    try {
      setIsUpdating(true);
      const response = await fetch("/api/orders/update-payment-status", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          orderId: order.id,
          status,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to update payment status");
      }

      showNotification("Payment status updated successfully", "success");
      // Refresh the order data
      window.location.reload();
    } catch (error) {
      console.error("Error updating payment status:", error);
      showNotification(
        error.message || "Failed to update payment status",
        "error"
      );
    } finally {
      setIsUpdating(false);
    }
  };

  const handleDeliveryFeeUpdate = async () => {
    try {
      setIsUpdatingDeliveryFee(true);
      const response = await fetch("/api/orders/update-delivery-fee", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          orderId: order.id,
          deliveryFee: parseFloat(newDeliveryFee),
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to update delivery fee");
      }

      // Update the order in state
      setOrder((prev) => ({
        ...prev,
        delivery_fee: parseFloat(newDeliveryFee),
        total_amount:
          prev.total_amount -
          (prev.delivery_fee || 0) +
          parseFloat(newDeliveryFee),
      }));

      showNotification("Delivery fee updated successfully", "success");
    } catch (error) {
      console.error("Error updating delivery fee:", error);
      showNotification(
        error.message || "Failed to update delivery fee",
        "error"
      );
    } finally {
      setIsUpdatingDeliveryFee(false);
    }
  };

  // Add this useEffect to initialize the newDeliveryFee state when order changes
  useEffect(() => {
    if (order) {
      setNewDeliveryFee(order.delivery_fee || 0);
    }
  }, [order]);

  // Fetch payment transactions for an order
  const fetchPaymentTransactions = async (orderId) => {
    try {
      const response = await window.ApiClient.getOrderPaymentTransactions(
        orderId
      );
      if (response.success) {
        setPaymentTransactions(response.transactions || []);
      }
    } catch (error) {
      console.error("Error fetching payment transactions:", error);
      setPaymentTransactions([]);
    }
  };

  // Check payment status
  const checkPaymentStatus = async (transactionId) => {
    if (!transactionId) {
      showNotification("No transaction ID available", "error");
      return;
    }

    setLoadingPaymentStatus(true);
    try {
      const response = await window.ApiClient.getPaymentStatus(transactionId);
      if (response.success) {
        showNotification("Payment status updated", "success");
        // Refresh order details and transactions
        if (order) {
          const orderDetails = await window.ApiClient.getAdminOrderDetails(
            order.id
          );
          if (orderDetails.order) {
            setOrder(orderDetails.order);
            setOrderItems(orderDetails.order.items || []);
          }
          fetchPaymentTransactions(order.id);
        }
      } else {
        showNotification(
          response.message || "Failed to check payment status",
          "error"
        );
      }
    } catch (error) {
      console.error("Error checking payment status:", error);
      showNotification("Failed to check payment status", "error");
    } finally {
      setLoadingPaymentStatus(false);
    }
  };

  // Retry payment
  const retryPayment = async (orderId) => {
    if (!orderId) {
      showNotification("Order ID not available", "error");
      return;
    }

    // Show confirmation for already paid orders
    if (order && order.payment_status === "paid") {
      const confirmed = window.confirm(
        "This order is already paid. Creating a new payment link will generate a new transaction. Do you want to continue?"
      );
      if (!confirmed) {
        return;
      }
    }

    setRetryingPayment(true);
    try {
      const response = await window.ApiClient.retryPayment(orderId);
      if (response.success && response.redirectUrl) {
        const message =
          order && order.payment_status === "paid"
            ? "New payment link generated. Opening in new tab..."
            : "Payment link generated. Opening in new tab...";
        showNotification(message, "info");
        // Open in new tab for admin
        window.open(response.redirectUrl, "_blank");
      } else {
        showNotification(
          response.message || "Failed to retry payment",
          "error"
        );
      }
    } catch (error) {
      console.error("Error retrying payment:", error);
      showNotification("Failed to retry payment", "error");
    } finally {
      setRetryingPayment(false);
    }
  };

  // Format currency values
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "INR",
      minimumFractionDigits: 2,
    }).format(amount);
  };

  // Format date in user's local timezone
  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    return formatToIST(dateString);
  };

  // Generate order status label with appropriate colors
  const getStatusLabel = (status) => {
    const statusColors = {
      placed: "bg-purple-100 text-purple-800",
      processing: "bg-blue-100 text-blue-800",
      shipped: "bg-indigo-100 text-indigo-800",
      delivered: "bg-green-100 text-green-800",
      cancelled: "bg-red-100 text-red-800",
    };

    return (
      <span
        className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium ${
          statusColors[status] || "bg-gray-100 text-gray-800"
        }`}
      >
        {status?.charAt(0).toUpperCase() + status?.slice(1) || "Unknown"}
      </span>
    );
  };

  // Get appropriate payment status color
  const getPaymentStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case "paid":
      case "completed":
        return "bg-green-100 text-green-800";
      case "pending":
      case "initiated":
        return "bg-yellow-100 text-yellow-800";
      case "failed":
        return "bg-red-100 text-red-800";
      case "cancelled":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  // Create receipt HTML template - matches ReceiptsManager exactly
  const createReceiptHTML = (order) => {
    const formatCurrency = (amount) => `₹${parseFloat(amount || 0).toFixed(2)}`;
    const formatDate = (dateString) => {
      if (!dateString) return "N/A";
      return formatToIST(dateString);
    };

    return `
      <div style="font-family: Arial, sans-serif; width: 140mm; margin: 0 auto; padding: 0; background: white; font-size: 12pt; line-height: 1.3; color: #000; position: static; overflow: visible; height: auto;">

        <!-- Company Header with proper spacing -->
        <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 6mm; padding-bottom: 3mm; border-bottom: 0.5pt solid #ddd; min-height: 22mm;">
          <div style="flex-shrink: 0;">
            <img src="/images/sreekarpublishers-logo.jpeg" alt="Sreekar Publishers" style="max-width: 28mm; max-height: 18mm; object-fit: contain; display: block;" />
          </div>
          <div style="text-align: right; max-width: 80mm; margin-left: 8mm;">
            <p style="font-weight: bold; font-size: 12pt; margin: 0 0 2mm 0; line-height: 1.3;">Sreekar Publishers</p>
            <p style="font-size: 8pt; line-height: 1.2; margin: 0;">9392333935, 6305842789, Eluru near DMart</p>
          </div>
        </div>

        <!-- Invoice Title and Order Info with proper spacing -->
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 6mm;">
          <div style="font-weight: bold; font-size: 16pt; text-transform: uppercase; margin: 0;">INVOICE</div>
          <div style="text-align: right; font-size: 10pt;">
            <p style="margin: 0;"><strong>Order #:</strong> ${
              order.order_number || "N/A"
            } | <strong>Date:</strong> ${formatDate(order.created_at)}</p>
          </div>
        </div>

        <!-- Customer and Order Info in 2 columns with proper spacing -->
        <div style="display: flex; justify-content: space-between; margin-bottom: 6mm; gap: 8mm;">
          <div style="width: 100%;">
            <table style="width: 100%; border-collapse: collapse; font-size: 10pt;">
              <tbody>
                <tr>
                  <td style="width: 25mm; padding: 1.5mm 0; vertical-align: top; font-weight: bold;">Customer:</td>
                  <td style="padding: 1.5mm 0; vertical-align: top;">${
                    order.address?.full_name || "N/A"
                  }</td>
                </tr>
                <tr>
                  <td style="padding: 1.5mm 0; vertical-align: top; font-weight: bold;">Phone:</td>
                  <td style="padding: 1.5mm 0; vertical-align: top;">${
                    order.customer?.phone || order.user_phone || "N/A"
                  }</td>
                </tr>
                <tr>
                  <td style="padding: 1.5mm 0; vertical-align: top; font-weight: bold;">WhatsApp:</td>
                  <td style="padding: 1.5mm 0; vertical-align: top;">${
                    order.address?.whatsapp_number || "N/A"
                  }</td>
                </tr>
                <tr>
                  <td style="padding: 1.5mm 0; vertical-align: top; font-weight: bold;">Status:</td>
                  <td style="padding: 1.5mm 0; vertical-align: top;">${
                    order.order_status || "N/A"
                  }</td>
                </tr>
                <tr>
                  <td style="padding: 1.5mm 0; vertical-align: top; font-weight: bold;">Payment:</td>
                  <td style="padding: 1.5mm 0; vertical-align: top;">${
                    order.payment_status || "N/A"
                  } (${order.payment_method || "N/A"})</td>
                </tr>
             
                ${
                  order.address
                    ? `
                <tr>
                  <td style="width: 25mm; padding: 1.5mm 0; vertical-align: top; font-weight: bold;">District:</td>
                  <td style="padding: 1.5mm 0; vertical-align: top;">${
                    order.address.district || "N/A"
                  }</td>
                </tr>
                <tr>
                  <td style="padding: 1.5mm 0; vertical-align: top; font-weight: bold;">Bus Stand:</td>
                  <td style="padding: 1.5mm 0; vertical-align: top;">${
                    order.address.nearest_busstand || "N/A"
                  }</td>
                </tr>
                <tr>
                  <td style="padding: 1.5mm 0; vertical-align: top; font-weight: bold;">School:</td>
                  <td style="padding: 1.5mm 0; vertical-align: top;">${
                    order.address.school_name || "N/A"
                  }</td>
                </tr>
                <tr>
                  <td style="padding: 1.5mm 0; vertical-align: top; font-weight: bold;">PIN Code:</td>
                  <td style="padding: 1.5mm 0; vertical-align: top;">${
                    order.address.zip_code || "N/A"
                  }</td>
                </tr>
                `
                    : `
                <tr>
                  <td colspan="2" style="padding: 1.5mm 0; font-style: italic; color: #666;">No address information available</td>
                </tr>
                `
                }
              </tbody>
            </table>
          </div>
        </div>

        <!-- Order Items Table with proper spacing and alignment -->
        <div style="margin-bottom: 6mm;">
          <table style="width: 100%; border-collapse: collapse; table-layout: fixed;">
            <thead>
              <tr style="background-color: #f5f5f5;">
                <th style="width: 70mm; padding: 3mm; font-size: 10pt; font-weight: bold; text-align: left; border: 0.5pt solid #ddd;">Item</th>
                <th style="width: 18mm; padding: 3mm; font-size: 10pt; font-weight: bold; text-align: center; border: 0.5pt solid #ddd;">Qty</th>
                <th style="width: 26mm; padding: 3mm; font-size: 10pt; font-weight: bold; text-align: right; border: 0.5pt solid #ddd;">Price</th>
                <th style="width: 26mm; padding: 3mm; font-size: 10pt; font-weight: bold; text-align: right; border: 0.5pt solid #ddd;">Total</th>
              </tr>
            </thead>
            <tbody>
              ${(order.items || [])
                .map(
                  (item) => `
                <tr>
                  <td style="padding: 3mm; font-size: 10pt; border: 0.5pt solid #ddd; text-align: left; vertical-align: top;">
                    ${item.product_name || "N/A"}
                  </td>
                  <td style="padding: 3mm; font-size: 10pt; border: 0.5pt solid #ddd; text-align: center; vertical-align: top;">${
                    item.quantity || 0
                  }</td>
                  <td style="padding: 3mm; font-size: 10pt; border: 0.5pt solid #ddd; text-align: right; vertical-align: top;">${formatCurrency(
                    item.product_price
                  )}</td>
                  <td style="padding: 3mm; font-size: 10pt; border: 0.5pt solid #ddd; text-align: right; vertical-align: top;">${formatCurrency(
                    item.total_price
                  )}</td>
                </tr>
              `
                )
                .join("")}
            </tbody>
          </table>
        </div>

        <!-- Order Summary with proper right alignment -->
        <div style="display: flex; justify-content: flex-end; margin-bottom: 6mm;">
          <div style="width: 70mm;">
            <table style="width: 100%; border-collapse: collapse;">
              <tbody>
                <tr>
                  <td style="padding: 1.5mm 3mm; font-size: 10pt; text-align: right; font-weight: bold; width: 45mm;">Subtotal:</td>
                  <td style="padding: 1.5mm 3mm; font-size: 10pt; text-align: right; width: 25mm;">${formatCurrency(
                    (order.total_amount || 0) -
                      (order.delivery_fee || 0) +
                      (order.discount_amount || 0)
                  )}</td>
                </tr>
                <tr>
                  <td style="padding: 1.5mm 3mm; font-size: 10pt; text-align: right; font-weight: bold;">Delivery Fee:</td>
                  <td style="padding: 1.5mm 3mm; font-size: 10pt; text-align: right;">${formatCurrency(
                    order.delivery_fee || 0
                  )}</td>
                </tr>
                ${
                  (order.discount_amount || 0) > 0
                    ? `
                <tr>
                  <td style="padding: 1.5mm 3mm; font-size: 10pt; text-align: right; font-weight: bold;">Discount ${
                    order.coupon_code ? `(${order.coupon_code})` : ""
                  }:</td>
                  <td style="padding: 1.5mm 3mm; font-size: 10pt; text-align: right;">-${formatCurrency(
                    order.discount_amount || 0
                  )}</td>
                </tr>
                `
                    : ""
                }
                <tr>
                  <td style="padding: 1.5mm 3mm; font-size: 10pt; text-align: right; font-weight: bold; border-top: 0.5pt solid #000;">Total:</td>
                  <td style="padding: 1.5mm 3mm; font-size: 10pt; text-align: right; font-weight: bold; border-top: 0.5pt solid #000;">${formatCurrency(
                    order.total_amount || 0
                  )}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- Special Instructions with proper spacing -->
        ${
          order.address && order.address.instructions
            ? `
        <div style="margin-bottom: 6mm; font-size: 10pt;">
          <p style="margin: 0;"><strong>Special Instructions:</strong> ${order.address.instructions}</p>
        </div>
        `
            : ""
        }

        <!-- Thank You Message with proper spacing -->
        <div style="text-align: center; font-size: 9pt; color: #666; border-top: 0.5pt solid #ddd; padding-top: 4mm; margin-top: 6mm;">
          <p style="margin: 0;">Thank you for your order! For any questions, please contact us at 9392333935, 6305842789</p>
        </div>
      </div>
    `;
  };

  // Generate and download PDF for single order
  const handleDownloadPDF = async () => {
    if (!order) return;

    try {
      setIsGeneratingPDF(true);
      setError(null);

      // Dynamically import jsPDF and html2canvas
      const { jsPDF } = await import("jspdf");
      const html2canvas = await import("html2canvas");

      const pdf = new jsPDF("p", "mm", "a4");

      // Create receipt HTML content
      const receiptHTML = createReceiptHTML(order);

      // Create temporary div for rendering
      const tempDiv = document.createElement("div");
      tempDiv.innerHTML = receiptHTML;
      tempDiv.style.position = "absolute";
      tempDiv.style.left = "-9999px";
      tempDiv.style.width = "210mm";
      tempDiv.style.backgroundColor = "white";
      tempDiv.style.padding = "20px";
      document.body.appendChild(tempDiv);

      try {
        // Convert to canvas
        const canvas = await html2canvas.default(tempDiv, {
          scale: 2,
          useCORS: true,
          allowTaint: true,
          backgroundColor: "#ffffff",
        });

        // Calculate dimensions
        const imgWidth = 210;
        const imgHeight = (canvas.height * imgWidth) / canvas.width;

        // Add to PDF
        pdf.addImage(
          canvas.toDataURL("image/png"),
          "PNG",
          0,
          0,
          imgWidth,
          Math.min(imgHeight, 297) // A4 height limit
        );
      } finally {
        // Remove temporary div
        document.body.removeChild(tempDiv);
      }

      // Generate filename
      const dateStr = new Date().toISOString().split("T")[0];
      const filename = `invoice_${order.order_number}_${dateStr}.pdf`;

      // Download PDF
      pdf.save(filename);

      showNotification("PDF downloaded successfully", "success");
    } catch (error) {
      console.error("Error generating PDF:", error);
      setError("Failed to generate PDF. Please try again.");
      showNotification("Failed to generate PDF", "error");
    } finally {
      setIsGeneratingPDF(false);
    }
  };

  // Display loading state
  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500 mb-4"></div>
        <p className="text-gray-600">Loading order details...</p>
      </div>
    );
  }

  // Display error state
  if (error) {
    return (
      <div className="rounded-md bg-red-50 p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <span className="material-icons-round text-red-400">
              error_outline
            </span>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">Error</h3>
            <div className="mt-2 text-sm text-red-700">
              <p>{error}</p>
            </div>
            <div className="mt-4">
              <button
                onClick={() => window.history.back()}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              >
                <span className="material-icons-round mr-1 text-sm">
                  arrow_back
                </span>
                Back to Orders
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Display if order is not found
  if (!order) {
    return (
      <div className="text-center py-12">
        <span className="material-icons-round text-gray-400 text-5xl mb-4">
          receipt_long
        </span>
        <h2 className="text-xl font-medium text-gray-900 mb-2">
          Order Not Found
        </h2>
        <p className="text-gray-500 mb-6">
          The order you're looking for doesn't exist or you don't have
          permission to view it.
        </p>
        <button
          onClick={() => window.history.back()}
          className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
        >
          <span className="material-icons-round mr-2">arrow_back</span>
          Back to Orders
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6 pb-10">
      {/* Header with back button and status display */}
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6">
        <div className="flex items-center mb-4 sm:mb-0">
          <button
            onClick={() => window.history.back()}
            className="mr-4 text-gray-500 hover:text-gray-700 focus:outline-none"
          >
            <span className="material-icons-round">arrow_back</span>
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Order #{order.order_number}
            </h1>
            <p className="text-gray-500">
              Placed on {formatDate(order.created_at)}
            </p>
          </div>
        </div>
        <div>
          <button
            onClick={handleDownloadPDF}
            disabled={isGeneratingPDF}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isGeneratingPDF ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-orange-500 mr-2"></div>
                Generating PDF...
              </>
            ) : (
              <>
                <span className="material-icons-round mr-2">download</span>
                Download PDF
              </>
            )}
          </button>
        </div>
      </div>

      {/* Status update success message */}
      {statusUpdateSuccess && (
        <div className="rounded-md bg-green-50 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="material-icons-round text-green-400">
                check_circle
              </span>
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-green-800">
                Order status updated successfully
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Order Information Grid */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {/* Left Column - Order Details */}
        <div className="space-y-6">
          {/* Order Status Section */}
          <section className="bg-white shadow-sm rounded-lg p-6 border border-gray-200">
            <h2 className="text-lg font-medium text-gray-900 mb-4">
              Order Status
            </h2>

            <div className="mb-6">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700">
                  Current Status:
                </span>
                {getStatusLabel(order.order_status)}
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">
                  Payment Status:
                </span>
                <span
                  className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium ${
                    order.payment_status === "paid"
                      ? "bg-green-100 text-green-800"
                      : order.payment_status === "pending"
                      ? "bg-yellow-100 text-yellow-800"
                      : "bg-red-100 text-red-800"
                  }`}
                >
                  {order.payment_status?.charAt(0).toUpperCase() +
                    order.payment_status?.slice(1) || "Unknown"}
                </span>
              </div>
            </div>

            <div className="print:hidden">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Update Status:
              </label>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                {[
                  "placed",
                  "processing",
                  "shipped",
                  "delivered",
                  "cancelled",
                ].map((status) => (
                  <button
                    key={status}
                    onClick={() => handleStatusChange(status)}
                    disabled={isUpdatingStatus || order.order_status === status}
                    className={`py-2 px-3 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 ${
                      order.order_status === status
                        ? "bg-gray-100 text-gray-800 font-medium cursor-default"
                        : status === "cancelled"
                        ? "bg-red-50 text-red-700 hover:bg-red-100"
                        : status === "delivered"
                        ? "bg-green-50 text-green-700 hover:bg-green-100"
                        : "bg-blue-50 text-blue-700 hover:bg-blue-100"
                    } ${
                      isUpdatingStatus ? "opacity-50 cursor-not-allowed" : ""
                    }`}
                  >
                    {status.charAt(0).toUpperCase() + status.slice(1)}
                  </button>
                ))}
              </div>
              {isUpdatingStatus && (
                <div className="flex justify-center mt-3">
                  <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-orange-500"></div>
                </div>
              )}
            </div>
          </section>

          {/* Customer Information */}
          <section className="bg-white shadow-sm rounded-lg p-6 border border-gray-200">
            <h2 className="text-lg font-medium text-gray-900 mb-4">
              Customer Information
            </h2>
            <div className="space-y-3">
              <div>
                <h3 className="text-sm font-medium text-gray-500">Name</h3>
                <p className="mt-1">{order.address.full_name || "N/A"}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500">Email</h3>
                <p className="mt-1">{order.customer.email || "N/A"}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500">Phone</h3>
                <p className="mt-1">{order.customer.phone || "N/A"}</p>
              </div>
            </div>
          </section>

          {/* Shipping Information */}
          <section className="bg-white shadow-sm rounded-lg p-6 border border-gray-200">
            <h2 className="text-lg font-medium text-gray-900 mb-4">
              Shipping Information
            </h2>
            <div className="space-y-4">
              {order.address ? (
                <>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">
                      Full Name
                    </h3>
                    <p className="mt-1 text-sm text-gray-900">
                      {order.address.full_name}
                    </p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">Phone</h3>
                    <p className="mt-1 text-sm text-gray-900">
                      {order.address.phone}
                    </p>
                  </div>
                  {/* <div>
                        <h3 className="text-sm font-medium text-gray-500">Address</h3>
                        <p className="mt-1 text-sm text-gray-900">{order.address.address}</p>
                      </div> */}
                  {/* <div>
                        <h3 className="text-sm font-medium text-gray-500">City</h3>
                        <p className="mt-1 text-sm text-gray-900">{order.address.city}</p>
                      </div> */}
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">
                      District
                    </h3>
                    <p className="mt-1 text-sm text-gray-900">
                      {order.address.district}
                    </p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">
                      Nearest Bus Stand
                    </h3>
                    <p className="mt-1 text-sm text-gray-900">
                      {order.address.nearest_busstand}
                    </p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">
                      School Name
                    </h3>
                    <p className="mt-1 text-sm text-gray-900">
                      {order.address.school_name}
                    </p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">
                      WhatsApp Number
                    </h3>
                    <p className="mt-1 text-sm text-gray-900">
                      {order.address.whatsapp_number}
                    </p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">
                      PIN Code
                    </h3>
                    <p className="mt-1 text-sm text-gray-900">
                      {order.address.zip_code}
                    </p>
                  </div>
                  {order.address.instructions && (
                    <div>
                      <h3 className="text-sm font-medium text-gray-500">
                        Delivery Instructions
                      </h3>
                      <p className="mt-1 text-sm text-gray-900">
                        {order.address.instructions}
                      </p>
                    </div>
                  )}
                </>
              ) : (
                <p className="text-sm text-gray-500">
                  No shipping information available
                </p>
              )}
            </div>
          </section>
        </div>

        {/* Right Column - Items & Payment */}
        <div className="space-y-6">
          {/* Order Items */}
          <section className="bg-white shadow-sm rounded-lg p-6 border border-gray-200">
            <h2 className="text-lg font-medium text-gray-900 mb-4">
              Order Items
            </h2>
            {orderItems.length > 0 ? (
              <div className="divide-y divide-gray-200">
                {orderItems.map((item) => (
                  <div key={item.id} className="py-4 flex">
                    {item.product_image && (
                      <div className="flex-shrink-0 w-16 h-16 rounded-md overflow-hidden border border-gray-200">
                        <img
                          src={item.product_image}
                          alt={item.product_name}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    )}
                    <div
                      className={`${item.product_image ? "ml-4" : ""} flex-1`}
                    >
                      <h3 className="text-sm font-medium text-gray-900">
                        {item.product_name}
                      </h3>
                      {/* Display unit information if available */}
                      {item.unit_type && item.unit_value && (
                        <p className="text-xs text-blue-600 bg-blue-50 px-2 py-0.5 rounded-md inline-block mt-1">
                          {item.unit_type === "quantity"
                            ? `${item.unit_value} ${
                                item.unit_value > 1 ? "items" : "item"
                              }`
                            : `${item.unit_value}${item.unit_type}`}
                        </p>
                      )}
                      <div className="flex justify-between mt-1">
                        <p className="text-sm text-gray-500">
                          {item.quantity} × {formatCurrency(item.product_price)}
                        </p>
                        <p className="text-sm font-medium text-gray-900">
                          {formatCurrency(item.total_price)}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500">No items found for this order</p>
            )}
          </section>

          {/* Payment Information */}
          <section className="bg-white shadow-sm rounded-lg p-6 border border-gray-200">
            <h2 className="text-lg font-medium text-gray-900 mb-4">
              Payment Information
            </h2>
            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium text-gray-500">
                  Payment Method
                </h3>
                <p className="mt-1 text-sm text-gray-900 capitalize">
                  {order.payment_method}
                </p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500">
                  Payment Status
                </h3>
                <div className="mt-1 flex items-center gap-4">
                  <span
                    className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium ${getPaymentStatusColor(
                      order.payment_status
                    )}`}
                  >
                    {order.payment_status?.charAt(0).toUpperCase() +
                      order.payment_status?.slice(1) || "Unknown"}
                  </span>
                  <div className="flex gap-2">
                    <button
                      onClick={() => handlePaymentStatusUpdate("paid")}
                      disabled={isUpdating || order.payment_status === "paid"}
                      className={`px-3 py-1 text-sm rounded-md ${
                        order.payment_status === "paid"
                          ? "bg-green-100 text-green-800"
                          : "bg-green-600 text-white hover:bg-green-700"
                      } disabled:opacity-50 disabled:cursor-not-allowed`}
                    >
                      Mark as Paid
                    </button>
                    <button
                      onClick={() => handlePaymentStatusUpdate("pending")}
                      disabled={
                        isUpdating || order.payment_status === "pending"
                      }
                      className={`px-3 py-1 text-sm rounded-md ${
                        order.payment_status === "pending"
                          ? "bg-yellow-100 text-yellow-800"
                          : "bg-yellow-600 text-white hover:bg-yellow-700"
                      } disabled:opacity-50 disabled:cursor-not-allowed`}
                    >
                      Mark as Pending
                    </button>
                  </div>
                </div>
              </div>

              {/* Payment Transactions */}
              {paymentTransactions.length > 0 && (
                <div className="border-t border-gray-200 pt-4">
                  <h3 className="text-sm font-medium text-gray-500 mb-3">
                    Payment Transactions ({paymentTransactions.length}):
                  </h3>
                  {paymentTransactions.length > 1 && (
                    <div className="bg-yellow-50 border border-yellow-200 rounded p-2 mb-3">
                      <p className="text-xs text-yellow-700 flex items-center">
                        <span className="material-icons-round text-[12px] mr-1">
                          info
                        </span>
                        Multiple payment attempts found. Latest transaction
                        shows current status.
                      </p>
                    </div>
                  )}
                  <div className="max-h-64 overflow-y-auto space-y-2">
                    {paymentTransactions.map((transaction) => (
                      <div
                        key={transaction.id}
                        className="bg-gray-50 rounded p-3 border border-gray-200"
                      >
                        <div className="flex justify-between items-start">
                          <div className="flex-1">
                            <p className="text-xs text-gray-600">
                              <span className="font-medium">Payment ID:</span>{" "}
                              {transaction.payment_id}
                            </p>
                            <p className="text-xs text-gray-600 mt-1">
                              <span className="font-medium">Amount:</span>{" "}
                              {formatCurrency(transaction.amount)}
                            </p>
                            <p className="text-xs text-gray-600 mt-1">
                              <span className="font-medium">Method:</span>{" "}
                              {transaction.payment_method}
                            </p>
                            <p className="text-xs text-gray-600 mt-1">
                              <span className="font-medium">Created:</span>{" "}
                              {formatDate(transaction.created_at)}
                            </p>
                            {transaction.updated_at !==
                              transaction.created_at && (
                              <p className="text-xs text-gray-600 mt-1">
                                <span className="font-medium">Updated:</span>{" "}
                                {formatDate(transaction.updated_at)}
                              </p>
                            )}
                          </div>
                          <div className="flex flex-col items-end space-y-1">
                            <span
                              className={`text-xs py-0.5 px-2 rounded font-medium ${getPaymentStatusColor(
                                transaction.status
                              )}`}
                            >
                              {transaction.status}
                            </span>
                            {/* Check Payment Status Button */}
                            {(transaction.status === "initiated" ||
                              transaction.status === "pending") && (
                              <button
                                onClick={() =>
                                  checkPaymentStatus(transaction.payment_id)
                                }
                                disabled={loadingPaymentStatus}
                                className="text-xs bg-blue-500 text-white px-2 py-1 rounded hover:bg-blue-600 transition-all disabled:opacity-50 flex items-center"
                              >
                                {loadingPaymentStatus ? (
                                  <>
                                    <div className="w-3 h-3 border border-white border-t-transparent rounded-full animate-spin mr-1"></div>
                                    Checking...
                                  </>
                                ) : (
                                  <>
                                    <span className="material-icons-round text-[12px] mr-1">
                                      refresh
                                    </span>
                                    Check Status
                                  </>
                                )}
                              </button>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Retry Payment Button for Online Payments */}
              {/* {order.payment_method === 'online' && (
                    <div className="border-t border-gray-200 pt-4">
                      <button
                        onClick={() => retryPayment(order.id)}
                        disabled={retryingPayment}
                        className={`w-full py-2 px-4 rounded-lg font-medium transition-all disabled:opacity-50 flex items-center justify-center ${
                          order.payment_status === 'paid'
                            ? 'bg-green-500 text-white hover:bg-green-600'
                            : 'bg-orange-500 text-white hover:bg-orange-600'
                        }`}
                      >
                        {retryingPayment ? (
                          <>
                            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                            Processing...
                          </>
                        ) : (
                          <>
                            <span className="material-icons-round mr-2 text-[18px]">
                              {order.payment_status === 'paid' ? 'refresh' : 'payment'}
                            </span>
                            {order.payment_status === 'paid' ? 'Generate New Payment Link' : 'Retry Payment'}
                          </>
                        )}
                      </button>
                      {order.payment_status === 'paid' && (
                        <p className="text-xs text-gray-500 mt-2 text-center">
                          This will create a new payment transaction for this order
                        </p>
                      )}
                    </div>
                  )} */}

              {order.payment_method === "online" &&
                order.payment_status === "paid" && (
                  <div>
                    <h3 className="text-sm font-medium text-gray-500">
                      Payment Date
                    </h3>
                    <p className="mt-1 text-sm text-gray-900">
                      {formatDate(order.updated_at)}
                    </p>
                  </div>
                )}
            </div>
          </section>

          {/* Order Summary */}
          <section className="bg-white shadow-sm rounded-lg p-6 border border-gray-200">
            <h2 className="text-lg font-medium text-gray-900 mb-4">
              Order Summary
            </h2>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <p className="text-gray-500">Subtotal</p>
                <p className="text-gray-900">
                  {formatCurrency(
                    order.total_amount -
                      (order.delivery_fee || 0) +
                      (order.discount_amount || 0)
                  )}
                </p>
              </div>
              <div className="flex justify-between text-sm items-center">
                <p className="text-gray-500">Delivery Fee</p>
                <div className="flex items-center gap-2">
                  <input
                    type="number"
                    min="0"
                    step="0.01"
                    value={newDeliveryFee}
                    onChange={(e) => setNewDeliveryFee(e.target.value)}
                    className="w-24 px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                    placeholder="Enter amount"
                  />
                  <button
                    onClick={handleDeliveryFeeUpdate}
                    disabled={
                      isUpdatingDeliveryFee ||
                      parseFloat(newDeliveryFee) === order.delivery_fee
                    }
                    className="px-3 py-1 text-sm rounded-md bg-orange-600 text-white hover:bg-orange-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isUpdatingDeliveryFee ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white"></div>
                    ) : (
                      "Update"
                    )}
                  </button>
                </div>
              </div>
              {order.discount_amount > 0 && (
                <div className="flex justify-between text-sm">
                  <p className="text-green-600 flex items-center">
                    <span className="material-icons-round text-xs mr-1">
                      local_offer
                    </span>
                    Discount {order.coupon_code ? `(${order.coupon_code})` : ""}
                  </p>
                  <p className="text-green-600">
                    -{formatCurrency(order.discount_amount || 0)}
                  </p>
                </div>
              )}
              <div className="border-t border-gray-200 mt-4 pt-4 flex justify-between">
                <p className="text-base font-medium text-gray-900">Total</p>
                <p className="text-base font-medium text-gray-900">
                  {formatCurrency(order.total_amount || 0)}
                </p>
              </div>
            </div>
          </section>

          {/* Additional Notes - only show if there's content */}
          {order.cancel_reason && (
            <section className="bg-white shadow-sm rounded-lg p-6 border border-gray-200">
              <h2 className="text-lg font-medium text-gray-900 mb-4">
                Additional Information
              </h2>
              <div className="space-y-3">
                <div>
                  <h3 className="text-sm font-medium text-gray-500">
                    Cancellation Reason
                  </h3>
                  <p className="mt-1 text-sm text-red-600">
                    {order.cancel_reason}
                  </p>
                </div>
              </div>
            </section>
          )}
        </div>
      </div>
    </div>
  );
}
