import React, { useState, useEffect } from "react";
import DataTable from "./DataTable";
import Modal from "../ui/Modal";
import { formatToIST } from "../../utils/dateUtils";

export default function ReceiptsManager() {
  const [orders, setOrders] = useState([]);
  const [allOrders, setAllOrders] = useState([]); // Store all orders for pagination
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [dateRange, setDateRange] = useState({
    fromDate: "",
    toDate: "",
  });
  const [selectedOrders, setSelectedOrders] = useState([]);
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false);
  const [showDateModal, setShowDateModal] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(50);
  const [totalOrders, setTotalOrders] = useState(0);
  const [isDateFiltered, setIsDateFiltered] = useState(false);

  // Fetch orders with date range (non-delivered orders, excluding cancelled)
  const fetchOrders = async (fromDate = "", toDate = "", page = 1) => {
    try {
      setIsLoading(true);
      setError(null);

      let result;
      const statusFilter = "pending,processing,shipped"; // Exclude cancelled and delivered orders

      if (fromDate || toDate) {
        // For date range filtering, get all results and handle pagination client-side
        setIsDateFiltered(true);
        result = await window.ApiClient.getAdminOrdersByDateRange(
          fromDate,
          toDate,
          statusFilter,
          ""
        );

        if (result.orders) {
          // Store all filtered orders
          setAllOrders(result.orders);
          setTotalOrders(result.orders.length);

          // Implement client-side pagination for date range results
          const startIndex = (page - 1) * itemsPerPage;
          const endIndex = startIndex + itemsPerPage;
          setOrders(result.orders.slice(startIndex, endIndex));
        }
      } else {
        // For normal pagination, use API pagination
        setIsDateFiltered(false);
        result = await window.ApiClient.getAdminOrders(
          page,
          itemsPerPage,
          statusFilter,
          ""
        );

        if (result.orders) {
          setAllOrders(result.orders);
          setOrders(result.orders);
          setTotalOrders(result.statusCounts.placed);
        }
      }
    } catch (error) {
      console.error("Error fetching orders:", error);
      setError("Failed to load orders. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    // Only fetch when page changes and no date filter is active
    if (!isDateFiltered) {
      fetchOrders("", "", currentPage);
    } else {
      // For date filtering, fetch with current date range and page
      fetchOrders(dateRange.fromDate, dateRange.toDate, currentPage);
    }
  }, [currentPage, isDateFiltered]);

  useEffect(() => {
    setCurrentPage(1); // Reset to first page when component mounts
    fetchOrders();
  }, []);

  // Handle date range filtering
  const handleDateFilter = () => {
    setCurrentPage(1); // Reset to first page when filtering
    fetchOrders(dateRange.fromDate, dateRange.toDate, 1);
    setShowDateModal(false);
  };

  // Handle order selection
  const handleOrderSelect = (orderId) => {
    setSelectedOrders((prev) => {
      if (prev.includes(orderId)) {
        return prev.filter((id) => id !== orderId);
      } else {
        return [...prev, orderId];
      }
    });
  };

  // Select all orders (only visible ones on current page)
  const handleSelectAll = () => {
    const visibleOrderIds = orders.map((order) => order.id);
    const allVisibleSelected = visibleOrderIds.every((id) =>
      selectedOrders.includes(id)
    );

    if (allVisibleSelected) {
      // Deselect all visible orders
      setSelectedOrders((prev) =>
        prev.filter((id) => !visibleOrderIds.includes(id))
      );
    } else {
      // Select all visible orders
      setSelectedOrders((prev) => {
        const newSelected = [...prev];
        visibleOrderIds.forEach((id) => {
          if (!newSelected.includes(id)) {
            newSelected.push(id);
          }
        });
        return newSelected;
      });
    }
  };

  // Check if all visible orders are selected
  const areAllVisibleSelected = () => {
    const visibleOrderIds = orders.map((order) => order.id);
    return (
      visibleOrderIds.length > 0 &&
      visibleOrderIds.every((id) => selectedOrders.includes(id))
    );
  };

  // Generate PDF for selected orders
  const generateReceiptsPDF = async () => {
    if (selectedOrders.length === 0) {
      setError("Please select at least one order to generate receipts.");
      return;
    }

    try {
      setIsGeneratingPDF(true);
      setError(null);

      // Get detailed order information
      const orderDetails = await Promise.all(
        selectedOrders.map((orderId) =>
          window.ApiClient.getAdminOrderDetails(orderId)
        )
      );

      // Generate PDF
      await generateBulkReceiptsPDF(orderDetails);
    } catch (error) {
      console.error("Error generating PDF:", error);
      setError("Failed to generate PDF. Please try again.");
    } finally {
      setIsGeneratingPDF(false);
    }
  };

  // PDF generation function
  const generateBulkReceiptsPDF = async (orderDetails) => {
    // Dynamically import jsPDF and html2canvas
    const { jsPDF } = await import("jspdf");
    const html2canvas = await import("html2canvas");

    const pdf = new jsPDF("p", "mm", "a4");
    let isFirstPage = true;

    for (const orderData of orderDetails) {
      const order = orderData.order;

      if (!isFirstPage) {
        pdf.addPage();
      }

      // Create receipt HTML content
      const receiptHTML = createReceiptHTML(order);

      // Create temporary div for rendering
      const tempDiv = document.createElement("div");
      tempDiv.innerHTML = receiptHTML;
      tempDiv.style.position = "absolute";
      tempDiv.style.left = "-9999px";
      tempDiv.style.width = "210mm";
      tempDiv.style.backgroundColor = "white";
      tempDiv.style.padding = "20px";
      document.body.appendChild(tempDiv);

      try {
        // Convert to canvas
        const canvas = await html2canvas.default(tempDiv, {
          scale: 2,
          useCORS: true,
          allowTaint: true,
          backgroundColor: "#ffffff",
        });

        // Calculate dimensions
        const imgWidth = 210;
        const imgHeight = (canvas.height * imgWidth) / canvas.width;

        // Add to PDF
        pdf.addImage(
          canvas.toDataURL("image/png"),
          "PNG",
          0,
          0,
          imgWidth,
          Math.min(imgHeight, 297) // A4 height limit
        );
      } finally {
        // Remove temporary div
        document.body.removeChild(tempDiv);
      }

      isFirstPage = false;
    }

    // Generate filename
    const dateStr = new Date().toISOString().split("T")[0];
    const filename = `receipts_${selectedOrders.length}_orders_${dateStr}.pdf`;

    // Download PDF
    pdf.save(filename);
  };

  // Create receipt HTML template - optimized for A5 printing with larger font sizes
  const createReceiptHTML = (order) => {
    const formatCurrency = (amount) => `₹${parseFloat(amount || 0).toFixed(2)}`;
    const formatDate = (dateString) => {
      if (!dateString) return "N/A";
      return formatToIST(dateString);
    };

    return `
       <div style="font-family: Arial, sans-serif; width: 140mm; margin: 0 auto; padding: 0; background: white; font-size: 12pt; line-height: 1.3; color: #000; position: static; overflow: visible; height: auto;">
 
         <!-- Company Header with proper spacing -->
         <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 6mm; padding-bottom: 3mm; border-bottom: 0.5pt solid #ddd; min-height: 22mm;">
           <div style="flex-shrink: 0;">
             <img src="/images/sreekarpublishers-logo.jpeg" alt="Sreekar Publishers" style="max-width: 28mm; max-height: 18mm; object-fit: contain; display: block;" />
           </div>
           <div style="text-align: right; max-width: 80mm; margin-left: 8mm;">
             <p style="font-weight: bold; font-size: 12pt; margin: 0 0 2mm 0; line-height: 1.3;">Sreekar Publishers</p>
             <p style="font-size: 8pt; line-height: 1.2; margin: 0;">9392333935, 6305842789, Eluru near DMart</p>
           </div>
         </div>
 
         <!-- Invoice Title and Order Info with proper spacing -->
         <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 6mm;">
           <div style="font-weight: bold; font-size: 16pt; text-transform: uppercase; margin: 0;">INVOICE</div>
           <div style="text-align: right; font-size: 10pt;">
             <p style="margin: 0;"><strong>Order #:</strong> ${
               order.order_number || "N/A"
             } | <strong>Date:</strong> ${formatDate(order.created_at)}</p>
           </div>
         </div>
 
         <!-- Customer and Order Info in 2 columns with proper spacing -->
         <div style="display: flex; justify-content: space-between; margin-bottom: 6mm; gap: 8mm;">
           <div style="width: 100%;">
             <table style="width: 100%; border-collapse: collapse; font-size: 10pt;">
               <tbody>
                 <tr>
                   <td style="width: 25mm; padding: 1.5mm 0; vertical-align: top; font-weight: bold;">Customer:</td>
                   <td style="padding: 1.5mm 0; vertical-align: top;">${
                     order.address?.full_name || "N/A"
                   }</td>
                 </tr>
                 <tr>
                   <td style="padding: 1.5mm 0; vertical-align: top; font-weight: bold;">Phone:</td>
                   <td style="padding: 1.5mm 0; vertical-align: top;">${
                     order.customer?.phone || order.user_phone || "N/A"
                   }</td>
                 </tr>
                 <tr>
                   <td style="padding: 1.5mm 0; vertical-align: top; font-weight: bold;">WhatsApp:</td>
                   <td style="padding: 1.5mm 0; vertical-align: top;">${
                     order.address?.whatsapp_number || "N/A"
                   }</td>
                 </tr>
                 <tr>
                   <td style="padding: 1.5mm 0; vertical-align: top; font-weight: bold;">Status:</td>
                   <td style="padding: 1.5mm 0; vertical-align: top;">${
                     order.order_status || "N/A"
                   }</td>
                 </tr>
                 <tr>
                   <td style="padding: 1.5mm 0; vertical-align: top; font-weight: bold;">Payment:</td>
                   <td style="padding: 1.5mm 0; vertical-align: top;">${
                     order.payment_status || "N/A"
                   } (${order.payment_method || "N/A"})</td>
                 </tr>
              
                 ${
                   order.address
                     ? `
                 <tr>
                   <td style="width: 25mm; padding: 1.5mm 0; vertical-align: top; font-weight: bold;">District:</td>
                   <td style="padding: 1.5mm 0; vertical-align: top;">${
                     order.address.district || "N/A"
                   }</td>
                 </tr>
                 <tr>
                   <td style="padding: 1.5mm 0; vertical-align: top; font-weight: bold;">Bus Stand:</td>
                   <td style="padding: 1.5mm 0; vertical-align: top;">${
                     order.address.nearest_busstand || "N/A"
                   }</td>
                 </tr>
                 <tr>
                   <td style="padding: 1.5mm 0; vertical-align: top; font-weight: bold;">School:</td>
                   <td style="padding: 1.5mm 0; vertical-align: top;">${
                     order.address.school_name || "N/A"
                   }</td>
                 </tr>
                 <tr>
                   <td style="padding: 1.5mm 0; vertical-align: top; font-weight: bold;">PIN Code:</td>
                   <td style="padding: 1.5mm 0; vertical-align: top;">${
                     order.address.zip_code || "N/A"
                   }</td>
                 </tr>
                 `
                     : `
                 <tr>
                   <td colspan="2" style="padding: 1.5mm 0; font-style: italic; color: #666;">No address information available</td>
                 </tr>
                 `
                 }
               </tbody>
             </table>
           </div>
         </div>
 
         <!-- Order Items Table with proper spacing and alignment -->
         <div style="margin-bottom: 6mm;">
           <table style="width: 100%; border-collapse: collapse; table-layout: fixed;">
             <thead>
               <tr style="background-color: #f5f5f5;">
                 <th style="width: 70mm; padding: 3mm; font-size: 10pt; font-weight: bold; text-align: left; border: 0.5pt solid #ddd;">Item</th>
                 <th style="width: 18mm; padding: 3mm; font-size: 10pt; font-weight: bold; text-align: center; border: 0.5pt solid #ddd;">Qty</th>
                 <th style="width: 26mm; padding: 3mm; font-size: 10pt; font-weight: bold; text-align: right; border: 0.5pt solid #ddd;">Price</th>
                 <th style="width: 26mm; padding: 3mm; font-size: 10pt; font-weight: bold; text-align: right; border: 0.5pt solid #ddd;">Total</th>
               </tr>
             </thead>
             <tbody>
               ${(order.items || [])
                 .map(
                   (item) => `
                 <tr>
                   <td style="padding: 3mm; font-size: 10pt; border: 0.5pt solid #ddd; text-align: left; vertical-align: top;">
                     ${item.product_name || "N/A"}
                   </td>
                   <td style="padding: 3mm; font-size: 10pt; border: 0.5pt solid #ddd; text-align: center; vertical-align: top;">${
                     item.quantity || 0
                   }</td>
                   <td style="padding: 3mm; font-size: 10pt; border: 0.5pt solid #ddd; text-align: right; vertical-align: top;">${formatCurrency(
                     item.product_price
                   )}</td>
                   <td style="padding: 3mm; font-size: 10pt; border: 0.5pt solid #ddd; text-align: right; vertical-align: top;">${formatCurrency(
                     item.total_price
                   )}</td>
                 </tr>
               `
                 )
                 .join("")}
             </tbody>
           </table>
         </div>
 
         <!-- Order Summary with proper right alignment -->
         <div style="display: flex; justify-content: flex-end; margin-bottom: 6mm;">
           <div style="width: 70mm;">
             <table style="width: 100%; border-collapse: collapse;">
               <tbody>
                 <tr>
                   <td style="padding: 1.5mm 3mm; font-size: 10pt; text-align: right; font-weight: bold; width: 45mm;">Subtotal:</td>
                   <td style="padding: 1.5mm 3mm; font-size: 10pt; text-align: right; width: 25mm;">${formatCurrency(
                     (order.total_amount || 0) -
                       (order.delivery_fee || 0) +
                       (order.discount_amount || 0)
                   )}</td>
                 </tr>
                 <tr>
                   <td style="padding: 1.5mm 3mm; font-size: 10pt; text-align: right; font-weight: bold;">Delivery Fee:</td>
                   <td style="padding: 1.5mm 3mm; font-size: 10pt; text-align: right;">${formatCurrency(
                     order.delivery_fee || 0
                   )}</td>
                 </tr>
                 ${
                   (order.discount_amount || 0) > 0
                     ? `
                 <tr>
                   <td style="padding: 1.5mm 3mm; font-size: 10pt; text-align: right; font-weight: bold;">Discount ${
                     order.coupon_code ? `(${order.coupon_code})` : ""
                   }:</td>
                   <td style="padding: 1.5mm 3mm; font-size: 10pt; text-align: right;">-${formatCurrency(
                     order.discount_amount || 0
                   )}</td>
                 </tr>
                 `
                     : ""
                 }
                 <tr>
                   <td style="padding: 1.5mm 3mm; font-size: 10pt; text-align: right; font-weight: bold; border-top: 0.5pt solid #000;">Total:</td>
                   <td style="padding: 1.5mm 3mm; font-size: 10pt; text-align: right; font-weight: bold; border-top: 0.5pt solid #000;">${formatCurrency(
                     order.total_amount || 0
                   )}</td>
                 </tr>
               </tbody>
             </table>
           </div>
         </div>
 
         <!-- Special Instructions with proper spacing -->
         ${
           order.address && order.address.instructions
             ? `
         <div style="margin-bottom: 6mm; font-size: 10pt;">
           <p style="margin: 0;"><strong>Special Instructions:</strong> ${order.address.instructions}</p>
         </div>
         `
             : ""
         }
 
         <!-- Thank You Message with proper spacing -->
         <div style="text-align: center; font-size: 9pt; color: #666; border-top: 0.5pt solid #ddd; padding-top: 4mm; margin-top: 6mm;">
           <p style="margin: 0;">Thank you for your order! For any questions, please contact us at 9392333935, 6305842789</p>
         </div>
       </div>
     `;
  };

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-IN", {
      style: "currency",
      currency: "INR",
      minimumFractionDigits: 2,
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    return formatToIST(dateString);
  };

  // Clear date filter
  const clearDateFilter = () => {
    setDateRange({ fromDate: "", toDate: "" });
    setCurrentPage(1); // Reset to first page when clearing filter
    setIsDateFiltered(false);
    fetchOrders("", "", 1);
  };

  // Column configuration for the DataTable
  const columns = [
    {
      key: "select",
      label: (
        <input
          type="checkbox"
          checked={areAllVisibleSelected()}
          onChange={handleSelectAll}
          className="rounded"
        />
      ),
      render: (row) => (
        <input
          type="checkbox"
          checked={selectedOrders.includes(row.id)}
          onChange={() => handleOrderSelect(row.id)}
          className="rounded"
        />
      ),
    },
    { key: "order_number", label: "Order #" },
    { key: "user_name", label: "Customer" },
    {
      key: "created_at",
      label: "Date",
      render: (row) => formatDate(row.created_at),
    },
    {
      key: "total_amount",
      label: "Total",
      render: (row) => formatCurrency(row.total_amount),
    },
    {
      key: "order_status",
      label: "Status",
      render: (row) => (
        <span
          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            row.order_status === "delivered"
              ? "bg-green-100 text-green-800"
              : row.order_status === "processing"
              ? "bg-blue-100 text-blue-800"
              : row.order_status === "shipped"
              ? "bg-indigo-100 text-indigo-800"
              : row.order_status === "cancelled"
              ? "bg-red-100 text-red-800"
              : "bg-purple-100 text-purple-800"
          }`}
        >
          {row.order_status.charAt(0).toUpperCase() + row.order_status.slice(1)}
        </span>
      ),
    },
  ];

  if (error && !isLoading) {
    return (
      <div className="rounded-md bg-red-50 p-4 mb-6">
        <div className="flex">
          <div className="flex-shrink-0">
            <span className="material-icons-round text-red-400">error</span>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">Error</h3>
            <div className="mt-2 text-sm text-red-700">
              <p>{error}</p>
            </div>
            <div className="mt-2">
              <button
                onClick={() => {
                  setError(null);
                  fetchOrders();
                }}
                className="px-3 py-1 text-sm font-medium text-red-800 bg-red-100 rounded-md hover:bg-red-200"
              >
                Try Again
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Date Range Modal */}
      {showDateModal && (
        <Modal
          title="Filter Orders by Date Range"
          onClose={() => setShowDateModal(false)}
        >
          <div className="p-6">
            <div className="mb-6">
              <div className="flex items-center justify-center w-12 h-12 mx-auto bg-blue-100 rounded-full mb-4">
                <span className="material-icons-round text-blue-600">
                  date_range
                </span>
              </div>
              <h3 className="text-lg font-medium text-center text-gray-900 mb-2">
                Select Date Range
              </h3>
              <p className="text-sm text-gray-500 text-center mb-6">
                Filter non-delivered orders by date range for receipt
                generation.
              </p>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    From Date
                  </label>
                  <input
                    type="date"
                    value={dateRange.fromDate}
                    onChange={(e) =>
                      setDateRange((prev) => ({
                        ...prev,
                        fromDate: e.target.value,
                      }))
                    }
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    To Date
                  </label>
                  <input
                    type="date"
                    value={dateRange.toDate}
                    onChange={(e) =>
                      setDateRange((prev) => ({
                        ...prev,
                        toDate: e.target.value,
                      }))
                    }
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                {/* Quick date presets */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Quick Select
                  </label>
                  <div className="grid grid-cols-2 gap-2">
                    <button
                      onClick={() => {
                        const today = new Date();
                        const lastWeek = new Date(
                          today.getTime() - 7 * 24 * 60 * 60 * 1000
                        );
                        setDateRange({
                          fromDate: lastWeek.toISOString().split("T")[0],
                          toDate: today.toISOString().split("T")[0],
                        });
                      }}
                      className="px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200"
                    >
                      Last 7 Days
                    </button>
                    <button
                      onClick={() => {
                        const today = new Date();
                        const lastMonth = new Date(
                          today.getTime() - 30 * 24 * 60 * 60 * 1000
                        );
                        setDateRange({
                          fromDate: lastMonth.toISOString().split("T")[0],
                          toDate: today.toISOString().split("T")[0],
                        });
                      }}
                      className="px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200"
                    >
                      Last 30 Days
                    </button>
                    <button
                      onClick={() => {
                        const today = new Date();
                        const thisMonth = new Date(
                          today.getFullYear(),
                          today.getMonth(),
                          1
                        );
                        setDateRange({
                          fromDate: thisMonth.toISOString().split("T")[0],
                          toDate: today.toISOString().split("T")[0],
                        });
                      }}
                      className="px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200"
                    >
                      This Month
                    </button>
                    <button
                      onClick={() => {
                        const today = new Date();
                        const lastMonth = new Date(
                          today.getFullYear(),
                          today.getMonth() - 1,
                          1
                        );
                        const lastMonthEnd = new Date(
                          today.getFullYear(),
                          today.getMonth(),
                          0
                        );
                        setDateRange({
                          fromDate: lastMonth.toISOString().split("T")[0],
                          toDate: lastMonthEnd.toISOString().split("T")[0],
                        });
                      }}
                      className="px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200"
                    >
                      Last Month
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowDateModal(false)}
                className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500"
              >
                Cancel
              </button>
              <button
                onClick={handleDateFilter}
                className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <span className="material-icons-round mr-2">filter_alt</span>
                Apply Filter
              </button>
            </div>
          </div>
        </Modal>
      )}

      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Order Receipts</h1>
            <p className="text-gray-600">
              Generate bulk PDF receipts for pending/processing/shipped orders
            </p>
          </div>
          <div className="flex items-center space-x-3">
            {/* Date Filter Button */}
            <button
              onClick={() => setShowDateModal(true)}
              className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <span className="material-icons-round mr-2">date_range</span>
              Date Filter
            </button>

            {/* Clear Filter Button */}
            {(dateRange.fromDate || dateRange.toDate) && (
              <button
                onClick={clearDateFilter}
                className="flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500"
              >
                <span className="material-icons-round mr-2">clear</span>
                Clear Filter
              </button>
            )}

            {/* Generate PDF Button */}
            <button
              onClick={generateReceiptsPDF}
              disabled={selectedOrders.length === 0 || isGeneratingPDF}
              className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isGeneratingPDF ? (
                <>
                  <svg
                    className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  Generating...
                </>
              ) : (
                <>
                  <span className="material-icons-round mr-2">
                    picture_as_pdf
                  </span>
                  Generate PDF ({selectedOrders.length})
                </>
              )}
            </button>
          </div>
        </div>

        {/* Applied Filters Display */}
        {(dateRange.fromDate || dateRange.toDate) && (
          <div className="flex items-center space-x-2 mb-4">
            <span className="text-sm text-gray-600">Active filters:</span>
            <div className="flex items-center space-x-2">
              {dateRange.fromDate && (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  From: {dateRange.fromDate}
                </span>
              )}
              {dateRange.toDate && (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  To: {dateRange.toDate}
                </span>
              )}
            </div>
          </div>
        )}

        {/* Selection Summary */}
        {selectedOrders.length > 0 && (
          <div className="bg-blue-50 border border-blue-200 rounded-md p-3 mb-4">
            <div className="flex items-center">
              <span className="material-icons-round text-blue-600 mr-2">
                info
              </span>
              <span className="text-sm text-blue-800">
                {selectedOrders.length} order
                {selectedOrders.length !== 1 ? "s" : ""} selected for PDF
                generation
              </span>
            </div>
          </div>
        )}
      </div>

      {/* Orders Table */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <DataTable
          columns={columns}
          data={orders}
          isLoading={isLoading}
          emptyMessage="No pending/processing/shipped orders found"
          pagination={true}
          itemsPerPage={itemsPerPage}
          paginationConfig={{
            currentPage: currentPage,
            totalItems: totalOrders,
            itemsPerPage: itemsPerPage,
            totalPages: Math.ceil(totalOrders / itemsPerPage),
            onPageChange: setCurrentPage,
          }}
        />

        {/* Pagination Controls - Always show when there are orders */}
        {orders.length > 0 && (
          <div className="px-6 py-4 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-700">
                Showing {(currentPage - 1) * itemsPerPage + 1} to{" "}
                {Math.min(currentPage * itemsPerPage, totalOrders)} of{" "}
                {totalOrders} orders
                {/* Debug info */}
                <span className="ml-2 text-xs text-gray-500">
                  (Page {currentPage}, Total: {totalOrders}, Per Page:{" "}
                  {itemsPerPage})
                </span>
              </div>

              {/* Only show navigation controls if there are multiple pages */}
              {totalOrders > itemsPerPage && (
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() =>
                      setCurrentPage((prev) => Math.max(prev - 1, 1))
                    }
                    disabled={currentPage === 1}
                    className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Previous
                  </button>

                  <div className="flex items-center space-x-1">
                    {Array.from(
                      { length: Math.ceil(totalOrders / itemsPerPage) },
                      (_, i) => i + 1
                    )
                      .filter((page) => {
                        // Show first page, last page, current page, and pages around current page
                        const totalPages = Math.ceil(
                          totalOrders / itemsPerPage
                        );
                        return (
                          page === 1 ||
                          page === totalPages ||
                          Math.abs(page - currentPage) <= 1
                        );
                      })
                      .map((page, index, array) => {
                        // Add ellipsis if there's a gap
                        const prevPage = array[index - 1];
                        const showEllipsis = prevPage && page - prevPage > 1;

                        return (
                          <React.Fragment key={page}>
                            {showEllipsis && (
                              <span className="px-2 py-1 text-sm text-gray-500">
                                ...
                              </span>
                            )}
                            <button
                              onClick={() => setCurrentPage(page)}
                              className={`px-3 py-1 text-sm rounded-md ${
                                currentPage === page
                                  ? "bg-blue-600 text-white"
                                  : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                              }`}
                            >
                              {page}
                            </button>
                          </React.Fragment>
                        );
                      })}
                  </div>

                  <button
                    onClick={() =>
                      setCurrentPage((prev) =>
                        Math.min(
                          prev + 1,
                          Math.ceil(totalOrders / itemsPerPage)
                        )
                      )
                    }
                    disabled={
                      currentPage === Math.ceil(totalOrders / itemsPerPage)
                    }
                    className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Next
                  </button>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
